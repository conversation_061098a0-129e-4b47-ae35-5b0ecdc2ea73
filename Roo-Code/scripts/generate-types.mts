import path from "path"
import fs from "fs"

import { zodToTs, createType<PERSON><PERSON>s, printNode } from "zod-to-ts"
import { $ } from "execa"

import schemas from "../src/schemas"

const { typeDefinitions } = schemas

async function main() {
	const types: string[] = [
		"// This file is automatically generated by running `npm run generate-types`\n// Do not edit it directly.",
	]

	for (const { schema, identifier } of typeDefinitions) {
		types.push(printNode(createTypeAlias(zodToTs(schema, identifier).node, identifier)))
		types.push(`export type { ${identifier} }`)
	}

	fs.writeFileSync("src/exports/types.ts", types.join("\n\n"))

	await $`npx tsup src/exports/interface.ts --dts -d out`
	fs.copyFileSync("out/interface.d.ts", "src/exports/roo-code.d.ts")

	await $`npx prettier --write src/exports/types.ts src/exports/roo-code.d.ts`

	if (fs.existsSync(path.join("..", "Roo-Code-Types"))) {
		fs.copyFileSync("out/interface.js", path.join("..", "Roo-Code-Types", "src", "index.js"))
		fs.copyFileSync("out/interface.d.ts", path.join("..", "Roo-Code-Types", "src", "index.d.ts"))
	}
}

main()
