{"greeting": "Roo Code へようこそ", "task": {"title": "タスク", "seeMore": "もっと見る", "seeLess": "表示を減らす", "tokens": "トークン:", "cache": "キャッシュ:", "apiCost": "APIコスト:", "contextWindow": "コンテキストウィンドウ:", "closeAndStart": "タスクを閉じて新しいタスクを開始", "export": "タスク履歴をエクスポート", "delete": "タスクを削除（Shift + クリックで確認をスキップ）", "condenseContext": "タスクコンテキストを圧縮"}, "unpin": "ピン留めを解除", "pin": "ピン留め", "tokenProgress": {"availableSpace": "利用可能な空き容量: {{amount}} トークン", "tokensUsed": "使用トークン: {{used}} / {{total}}", "reservedForResponse": "モデル応答用に予約: {{amount}} トークン"}, "retry": {"title": "再試行", "tooltip": "操作を再試行"}, "startNewTask": {"title": "新しいタスクを開始", "tooltip": "新しいタスクを開始"}, "proceedAnyways": {"title": "それでも続行", "tooltip": "コマンド実行中でも続行"}, "save": {"title": "保存", "tooltip": "ファイル変更を保存"}, "reject": {"title": "拒否", "tooltip": "このアクションを拒否"}, "completeSubtaskAndReturn": "サブタスクを完了して戻る", "approve": {"title": "承認", "tooltip": "このアクションを承認"}, "runCommand": {"title": "コマンド実行", "tooltip": "このコマンドを実行"}, "proceedWhileRunning": {"title": "実行中も続行", "tooltip": "警告にもかかわらず続行"}, "killCommand": {"title": "コマンドを強制終了", "tooltip": "現在のコマンドを強制終了します"}, "resumeTask": {"title": "タスクを再開", "tooltip": "現在のタスクを続行"}, "terminate": {"title": "終了", "tooltip": "現在のタスクを終了"}, "cancel": {"title": "キャンセル", "tooltip": "現在の操作をキャンセル"}, "scrollToBottom": "チャットの最下部にスクロール", "about": "AI支援でコードを生成、リファクタリング、デバッグします。詳細については、<DocsLink>ドキュメント</DocsLink>をご覧ください。", "onboarding": "最新のエージェント型コーディング能力の進歩により、複雑なソフトウェア開発タスクをステップバイステップで処理できます。ファイルの作成や編集、複雑なプロジェクトの探索、ブラウザの使用、ターミナルコマンドの実行（許可後）を可能にするツールにより、コード補完や技術サポート以上の方法であなたをサポートできます。MCPを使用して新しいツールを作成し、自分の能力を拡張することもできます。", "rooTips": {"boomerangTasks": {"title": "ブーメランタスク", "description": "タスクをより小さく、管理しやすい部分に分割します。"}, "stickyModels": {"title": "スティッキーモード", "description": "各モードは、最後に使用したモデルを記憶しています"}, "tools": {"title": "ツール", "description": "AIがWebの閲覧、コマンドの実行などによって問題を解決できるようにします。"}, "customizableModes": {"title": "カスタマイズ可能なモード", "description": "独自の動作と割り当てられたモデルを持つ専門的なペルソナ"}}, "selectMode": "対話モードを選択", "selectApiConfig": "API構成を選択", "enhancePrompt": "追加コンテキストでプロンプトを強化", "addImages": "メッセージに画像を追加", "sendMessage": "メッセージを送信", "typeMessage": "メッセージを入力...", "typeTask": "ここにタスクを入力...", "addContext": "コンテキスト追加は@、モード切替は/", "dragFiles": "ファイルをドラッグするにはShiftキーを押したまま", "dragFilesImages": "ファイル/画像をドラッグするにはShiftキーを押したまま", "enhancePromptDescription": "「プロンプトを強化」ボタンは、追加コンテキスト、説明、または言い換えを提供することで、リクエストを改善します。ここにリクエストを入力し、ボタンを再度クリックして動作を確認してください。", "errorReadingFile": "ファイル読み込みエラー:", "noValidImages": "有効な画像が処理されませんでした", "separator": "区切り", "edit": "編集...", "forNextMode": "次のモード用", "error": "エラー", "diffError": {"title": "編集に失敗しました"}, "troubleMessage": "Rooに問題が発生しています...", "apiRequest": {"title": "APIリクエスト", "failed": "APIリクエスト失敗", "streaming": "APIリクエスト...", "cancelled": "APIリクエストキャンセル", "streamingFailed": "APIストリーミング失敗"}, "checkpoint": {"initial": "初期チェックポイント", "regular": "チェックポイント", "initializingWarning": "チェックポイントの初期化中... 時間がかかりすぎる場合は、<settingsLink>設定</settingsLink>でチェックポイントを無効にしてタスクを再開できます。", "menu": {"viewDiff": "差分を表示", "restore": "チェックポイントを復元", "restoreFiles": "ファイルを復元", "restoreFilesDescription": "この時点で撮影されたスナップショットにプロジェクトのファイルを復元します。", "restoreFilesAndTask": "ファイルとタスクを復元", "confirm": "確認", "cancel": "キャンセル", "cannotUndo": "このアクションは元に戻せません。", "restoreFilesAndTaskDescription": "この時点で撮影されたスナップショットにプロジェクトのファイルを復元し、この時点以降のすべてのメッセージを削除します。"}, "current": "現在"}, "instructions": {"wantsToFetch": "Rooは現在のタスクを支援するための詳細な指示を取得したい"}, "fileOperations": {"wantsToRead": "Rooはこのファイルを読みたい:", "wantsToReadOutsideWorkspace": "Rooはワークスペース外のこのファイルを読みたい:", "didRead": "Rooはこのファイルを読みました:", "wantsToEdit": "Rooはこのファイルを編集したい:", "wantsToEditOutsideWorkspace": "Rooはワークスペース外のこのファイルを編集したい:", "wantsToCreate": "<PERSON><PERSON>は新しいファイルを作成したい:", "wantsToSearchReplace": "Rooはこのファイルで検索と置換を行う:", "didSearchReplace": "Rooはこのファイルで検索と置換を実行しました:", "wantsToInsert": "Rooはこのファイルにコンテンツを挿入したい:", "wantsToInsertWithLineNumber": "Rooはこのファイルの{{lineNumber}}行目にコンテンツを挿入したい:", "wantsToInsertAtEnd": "Rooはこのファイルの末尾にコンテンツを追加したい:"}, "directoryOperations": {"wantsToViewTopLevel": "Rooはこのディレクトリのトップレベルファイルを表示したい:", "didViewTopLevel": "Rooはこのディレクトリのトップレベルファイルを表示しました:", "wantsToViewRecursive": "Rooはこのディレクトリのすべてのファイルを再帰的に表示したい:", "didViewRecursive": "Rooはこのディレクトリのすべてのファイルを再帰的に表示しました:", "wantsToViewDefinitions": "Rooはこのディレクトリで使用されているソースコード定義名を表示したい:", "didViewDefinitions": "Rooはこのディレクトリで使用されているソースコード定義名を表示しました:", "wantsToSearch": "Rooはこのディレクトリで <code>{{regex}}</code> を検索したい:", "didSearch": "Rooはこのディレクトリで <code>{{regex}}</code> を検索しました:"}, "commandOutput": "コマンド出力", "response": "応答", "arguments": "引数", "mcp": {"wantsToUseTool": "RooはMCPサーバー{{serverName}}でツールを使用したい:", "wantsToAccessResource": "RooはMCPサーバー{{serverName}}のリソースにアクセスしたい:"}, "modes": {"wantsToSwitch": "<PERSON>oo<PERSON><code>{{mode}}</code>モードに切り替えたい", "wantsToSwitchWithReason": "Rooは次の理由で<code>{{mode}}</code>モードに切り替えたい: {{reason}}", "didSwitch": "<PERSON>oo<PERSON><code>{{mode}}</code>モードに切り替えました", "didSwitchWithReason": "Rooは次の理由で<code>{{mode}}</code>モードに切り替えました: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON><code>{{mode}}</code>モードで新しいサブタスクを作成したい:", "wantsToFinish": "Rooはこのサブタスクを終了したい", "newTaskContent": "サブタスク指示", "completionContent": "サブタスク完了", "resultContent": "サブタスク結果", "defaultResult": "次のタスクに進んでください。", "completionInstructions": "サブタスク完了！結果を確認し、修正や次のステップを提案できます。問題なければ、親タスクに結果を返すために確認してください。"}, "questions": {"hasQuestion": "<PERSON><PERSON>は質問があります:"}, "taskCompleted": "タスク完了", "powershell": {"issues": "Windows PowerShellに問題があるようです。こちらを参照してください"}, "autoApprove": {"title": "自動承認:", "none": "なし", "description": "自動承認はRoo Codeに許可を求めずに操作を実行する権限を与えます。完全に信頼できる操作のみ有効にしてください。より詳細な設定は<settingsLink>設定</settingsLink>で利用できます。"}, "reasoning": {"thinking": "考え中", "seconds": "{{count}}秒"}, "contextCondense": {"title": "コンテキスト要約", "condensing": "コンテキストを圧縮中...", "tokens": "トークン"}, "followUpSuggest": {"copyToInput": "入力欄にコピー（またはShift + クリック）"}, "announcement": {"title": "🎉 Roo Code {{version}} リリース", "description": "Roo Code {{version}}は、あなたのフィードバックに基づく強力な新機能と改善をもたらします。", "whatsNew": "新機能", "feature1": "<bold>Geminiの暗黙的キャッシング</bold>: Gemini APIコールが自動的にキャッシュされるようになり、APIコストが削減されます", "feature2": "<bold>よりスマートなモード選択</bold>: モード定義に各モードをいつ使用すべきかの指針を含めることができるようになり、より優れた調整が可能になります", "feature3": "<bold>インテリジェントなコンテキスト圧縮</bold>: コンテキストが一杯になったときに切り捨てる代わりに、会話履歴をインテリジェントに要約します（設定 -> 実験的機能で有効化）", "hideButton": "通知を非表示", "detailsDiscussLinks": "詳細は<discordLink>Discord</discordLink>と<redditLink>Reddit</redditLink>でご確認・ディスカッションください 🚀"}, "browser": {"rooWantsToUse": "Rooはブラウザを使用したい:", "consoleLogs": "コンソールログ", "noNewLogs": "(新しいログはありません)", "screenshot": "ブラウザのスクリーンショット", "cursor": "カーソル", "navigation": {"step": "ステップ {{current}} / {{total}}", "previous": "前へ", "next": "次へ"}, "sessionStarted": "ブラウザセッション開始", "actions": {"title": "ブラウザアクション: ", "launch": "{{url}} でブラウザを起動", "click": "クリック ({{coordinate}})", "type": "入力 \"{{text}}\"", "scrollDown": "下にスクロール", "scrollUp": "上にスクロール", "close": "ブラウザを閉じる"}}, "codeblock": {"tooltips": {"expand": "コードブロックを展開", "collapse": "コードブロックを折りたたむ", "enable_wrap": "折り返しを有効化", "disable_wrap": "折り返しを無効化", "copy_code": "コードをコピー"}}, "systemPromptWarning": "警告：カスタムシステムプロンプトの上書きが有効です。これにより機能が深刻に損なわれ、予測不可能な動作が発生する可能性があります。", "shellIntegration": {"title": "コマンド実行警告", "description": "コマンドはVSCodeターミナルシェル統合なしで実行されています。この警告を非表示にするには、<settingsLink>Roo Code設定</settingsLink>の<strong>Terminal</strong>セクションでシェル統合を無効にするか、以下のリンクを使用してVSCodeターミナル統合のトラブルシューティングを行ってください。", "troubleshooting": "シェル統合のドキュメントはこちらをクリック"}, "ask": {"autoApprovedRequestLimitReached": {"title": "自動承認リクエスト制限に達しました", "description": "Rooは{{count}}件のAPI自動承認リクエスト制限に達しました。カウントをリセットしてタスクを続行しますか？", "button": "リセットして続行"}}}