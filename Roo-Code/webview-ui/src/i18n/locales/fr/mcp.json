{"title": "Serveurs MCP", "done": "<PERSON><PERSON><PERSON><PERSON>", "description": "Active le Model Context Protocol (MCP) pour permettre à Roo Code d'utiliser des outils et services supplémentaires depuis des serveurs externes. Cela élargit ce que Roo peut faire pour toi. <0>En savoir plus</0>", "enableToggle": {"title": "Activer les serveurs MCP", "description": "Active cette option pour que Roo puisse utiliser des outils provenant de serveurs MCP connectés. Cela donne plus de capacités à Roo. Si tu ne comptes pas utiliser ces outils supplémentaires, désactive-la pour réduire les coûts de tokens API."}, "enableServerCreation": {"title": "Activer la création de serveurs MCP", "description": "Active cette option pour que Roo t'aide à créer de <1>nouveaux</1> serveurs MCP personnalisés. <0>En savoir plus sur la création de serveurs</0>", "hint": "Astuce : Pour réduire les coûts de tokens API, désactive cette option quand tu ne demandes pas à Roo de créer un nouveau serveur MCP."}, "editGlobalMCP": "Modifier le MCP global", "editProjectMCP": "Modifier le MCP du projet", "learnMoreEditingSettings": "En savoir plus sur la modification des fichiers de configuration MCP", "tool": {"alwaysAllow": "Toujours autoriser", "parameters": "Paramètres", "noDescription": "Aucune description"}, "tabs": {"tools": "Outils", "resources": "Ressources", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON><PERSON><PERSON> outil trouvé", "noResources": "<PERSON><PERSON><PERSON> ressource trouvée", "noLogs": "Aucun journal trouvé", "noErrors": "<PERSON><PERSON><PERSON> erreur trouvée"}, "networkTimeout": {"label": "<PERSON><PERSON><PERSON>'attente r<PERSON>", "description": "Temps d'attente maximal pour les réponses du serveur", "options": {"15seconds": "15 secondes", "30seconds": "30 secondes", "1minute": "1 minute", "5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "30minutes": "30 minutes", "60minutes": "60 minutes"}}, "deleteDialog": {"title": "Supp<PERSON>er le serveur MCP", "description": "Es-tu sûr de vouloir supprimer le serveur MCP \"{{serverName}}\" ? Cette action est irréversible.", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "serverStatus": {"retrying": "Nouvelle tentative...", "retryConnection": "Réessayer la connexion"}}