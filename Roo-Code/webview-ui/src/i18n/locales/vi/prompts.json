{"title": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON> th<PERSON>", "modes": {"title": "<PERSON><PERSON> độ", "createNewMode": "<PERSON><PERSON><PERSON> chế độ mới", "editModesConfig": "Chỉnh sửa cấu hình chế độ", "editGlobalModes": "Chỉnh sửa chế độ toàn cục", "editProjectModes": "Chỉnh sửa chế độ dự án (.roomodes)", "createModeHelpText": "Chế độ là các vai trò chuyên biệt điều chỉnh hành vi của Roo. <0>Tìm hiểu về Sử dụng Chế độ</0> hoặc <1>Tùy chỉnh Chế độ.</1>", "selectMode": "<PERSON><PERSON><PERSON> ki<PERSON>m chế độ"}, "apiConfiguration": {"title": "<PERSON><PERSON><PERSON> h<PERSON>", "select": "<PERSON><PERSON><PERSON> cấu hình <PERSON> nào để sử dụng cho chế độ này"}, "tools": {"title": "<PERSON><PERSON>ng cụ có sẵn", "builtInModesText": "<PERSON><PERSON><PERSON> cụ cho các chế độ tích hợp sẵn không thể được sửa đổi", "editTools": "Chỉnh sửa công cụ", "doneEditing": "<PERSON><PERSON><PERSON> thành chỉnh sửa", "allowedFiles": "<PERSON><PERSON><PERSON> đ<PERSON> ph<PERSON>:", "toolNames": {"read": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa tệp", "browser": "Sử dụng trình <PERSON>", "command": "<PERSON><PERSON><PERSON>", "mcp": "Sử dụng MCP"}, "noTools": "<PERSON><PERSON><PERSON><PERSON> có"}, "roleDefinition": {"title": "<PERSON><PERSON><PERSON> ngh<PERSON>a vai trò", "resetToDefault": "Đặt lại về mặc định", "description": "<PERSON><PERSON><PERSON> định chuyên môn và tính cách của Roo cho chế độ này. <PERSON><PERSON> tả này định hình cách Roo giới thiệu bản thân và tiếp cận nhiệm vụ."}, "whenToUse": {"title": "<PERSON><PERSON> <PERSON><PERSON>o nên sử dụng (tù<PERSON> chọn)", "description": "<PERSON><PERSON> tả khi nào nên sử dụng chế độ này. <PERSON><PERSON><PERSON>u này giúp Orchestrator chọn chế độ phù hợp cho một nhiệm vụ.", "resetToDefault": "Đặt lại mô tả '<PERSON>hi nào nên sử dụng' về mặc định"}, "customInstructions": {"title": "Hướng dẫn tùy chỉnh dành riêng cho chế độ (tù<PERSON> ch<PERSON>n)", "resetToDefault": "Đặt lại về mặc định", "description": "<PERSON><PERSON><PERSON><PERSON> hướng dẫn hành vi dành riêng cho chế độ {{modeName}}.", "loadFromFile": "Hướng dẫn tùy chỉnh dành riêng cho chế độ {{mode}} cũng có thể được tải từ thư mục <span>.roo/rules-{{slug}}/</span> trong không gian làm việc của bạn (.roorules-{{slug}} và .clinerules-{{slug}} đã lỗi thời và sẽ sớm ngừng hoạt động)."}, "globalCustomInstructions": {"title": "Hướng dẫn tùy chỉnh cho tất cả các chế độ", "description": "<PERSON>h<PERSON>ng hướng dẫn này áp dụng cho tất cả các chế độ. Chúng cung cấp một bộ hành vi cơ bản có thể được nâng cao bởi hướng dẫn dành riêng cho chế độ bên dưới. <0>Tìm hiểu thêm</0>", "loadFromFile": "Hướng dẫn cũng có thể được tải từ thư mục <span>.roo/rules/</span> trong không gian làm việc của bạn (.roorules và .clinerules đã lỗi thời và sẽ sớm ngừng hoạt động)."}, "systemPrompt": {"preview": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> lờ<PERSON> nh<PERSON>c hệ thống", "copy": "<PERSON><PERSON> ch<PERSON><PERSON> lời nh<PERSON>c hệ thống vào bộ nhớ tạm", "title": "<PERSON><PERSON><PERSON> <PERSON><PERSON> hệ thống (chế độ {{modeName}})"}, "supportPrompts": {"title": "<PERSON><PERSON><PERSON> nhắc hỗ trợ", "resetPrompt": "Đặt lại lời nhắc {{promptType}} về mặc định", "prompt": "<PERSON><PERSON><PERSON>", "enhance": {"apiConfiguration": "<PERSON><PERSON><PERSON> h<PERSON>", "apiConfigDescription": "Bạn có thể chọn cấu hình API để luôn sử dụng cho việc nâng cao lời nhắc, hoặc chỉ sử dụng bất cứ cấu hình nào hiện đang được chọn", "useCurrentConfig": "<PERSON><PERSON> dụng cấu hình <PERSON> hiện tại đã chọn", "testPromptPlaceholder": "<PERSON><PERSON><PERSON><PERSON> lời nhắc để kiểm tra việc nâng cao", "previewButton": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> nâng cao lời nh<PERSON>c"}, "types": {"ENHANCE": {"label": "<PERSON><PERSON><PERSON> cao lời n<PERSON>c", "description": "Sử dụng nâng cao lời nhắc để nhận đề xuất hoặc cải tiến phù hợp cho đầu vào của bạn. Điều này đảm bảo Roo hiểu ý định của bạn và cung cấp phản hồi tốt nhất có thể. Có sẵn thông qua biểu tượng ✨ trong chat."}, "EXPLAIN": {"label": "<PERSON><PERSON><PERSON><PERSON> thích mã", "description": "Nhận giải thích chi tiết về đoạn mã, hàm hoặc toàn bộ tệp. Hữu ích để hiểu mã phức tạp hoặc học các mẫu mới. Có sẵn trong hành động mã (biểu tượng bóng đèn trong trình soạn thảo) và menu ngữ cảnh trình soạn thảo (nhấp chuột phải vào mã đã chọn)."}, "FIX": {"label": "<PERSON><PERSON><PERSON> vấn đề", "description": "Nhận trợ giúp xác định và giải quyết lỗi, sai sót hoặc vấn đề chất lượng mã. Cung cấp hướng dẫn từng bước để sửa chữa vấn đề. Có sẵn trong hành động mã (biểu tượng bóng đèn trong trình soạn thảo) và menu ngữ cảnh trình soạn thảo (nhấp chuột phải vào mã đã chọn)."}, "IMPROVE": {"label": "<PERSON><PERSON><PERSON> thiện mã", "description": "Nhận đề xuất tối ưu hóa mã, thực hành tốt hơn và cải tiến kiến trúc trong khi duy trì chức năng. Có sẵn trong hành động mã (biểu tượng bóng đèn trong trình soạn thảo) và menu ngữ cảnh trình soạn thảo (nhấp chuột phải vào mã đã chọn)."}, "ADD_TO_CONTEXT": {"label": "<PERSON><PERSON><PERSON><PERSON> vào ngữ cảnh", "description": "Thêm ngữ cảnh vào nhiệm vụ hoặc cuộc trò chuyện hiện tại của bạn. Hữu ích để cung cấp thông tin bổ sung hoặc làm rõ. Có sẵn trong hành động mã (biểu tượng bóng đèn trong trình soạn thảo) và menu ngữ cảnh trình soạn thảo (nhấp chuột phải vào mã đã chọn)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "<PERSON><PERSON><PERSON><PERSON> nội dung terminal vào ngữ cảnh", "description": "Thêm đầu ra terminal vào nhiệm vụ hoặc cuộc trò chuyện hiện tại của bạn. Hữu ích để cung cấp đầu ra lệnh hoặc nhật ký. Có sẵn trong menu ngữ cảnh terminal (nhấp chuột phải vào nội dung terminal đã chọn)."}, "TERMINAL_FIX": {"label": "Sửa lệnh terminal", "description": "Nhận trợ giúp sửa lệnh terminal đã thất bại hoặc cần cải thiện. Có sẵn trong menu ngữ cảnh terminal (nhấp chuột phải vào nội dung terminal đã chọn)."}, "TERMINAL_EXPLAIN": {"label": "Giải th<PERSON>ch l<PERSON> terminal", "description": "Nhận giải thích chi tiết về lệnh terminal và đầu ra của chúng. Có sẵn trong menu ngữ cảnh terminal (nhấp chuột phải vào nội dung terminal đã chọn)."}, "NEW_TASK": {"label": "<PERSON>ắt đ<PERSON>u tác vụ mới", "description": "Bắt đầu tác vụ mới với nội dung đã nhập. Có sẵn trong bảng lệnh."}}}, "advancedSystemPrompt": {"title": "Nâng cao: <PERSON><PERSON> đè lời nhắc hệ thống", "description": "<2>⚠️ Cảnh báo:</2> <PERSON><PERSON><PERSON> năng nâng cao này bỏ qua các biện pháp bảo vệ. <1>ĐỌC KỸ TRƯỚC KHI SỬ DỤNG!</1>Ghi đè lời nhắc hệ thống mặc định bằng cách tạo một tệp tại <span>.roo/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "<PERSON><PERSON><PERSON> chế độ mới", "close": "Đ<PERSON><PERSON>", "name": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên chế độ"}, "slug": {"label": "Slug", "description": "Slug được sử dụng trong URL và tên tệp. <PERSON><PERSON>ê<PERSON> viết thường và chỉ chứa chữ cái, số và dấu gạch ngang."}, "saveLocation": {"label": "<PERSON><PERSON> trí lưu", "description": "<PERSON>ọn nơi lưu chế độ này. Chế độ dành riêng cho dự án được ưu tiên hơn chế độ toàn cục.", "global": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> sẵn trong tất cả các không gian làm việc"}, "project": {"label": "<PERSON><PERSON><PERSON> riêng cho dự án (.roomodes)", "description": "Chỉ có sẵn trong không gian làm vi<PERSON><PERSON> nà<PERSON>, đ<PERSON><PERSON><PERSON> ưu tiên hơn toàn cục"}}, "roleDefinition": {"label": "<PERSON><PERSON><PERSON> ngh<PERSON>a vai trò", "description": "<PERSON><PERSON><PERSON> đ<PERSON>nh chuyên môn và tính cách của Roo cho chế độ này."}, "whenToUse": {"label": "<PERSON><PERSON> <PERSON><PERSON>o nên sử dụng (tù<PERSON> chọn)", "description": "<PERSON><PERSON> cấp mô tả rõ ràng về thời điểm chế độ này hiệu quả nhất và những loại nhiệm vụ nào nó thực hiện tốt nhất."}, "tools": {"label": "<PERSON><PERSON>ng cụ có sẵn", "description": "<PERSON><PERSON><PERSON> công cụ nào chế độ này có thể sử dụng."}, "customInstructions": {"label": "Hướng dẫn tùy chỉnh (tù<PERSON> chọn)", "description": "<PERSON><PERSON><PERSON><PERSON> hướng dẫn hành vi dành riêng cho chế độ này."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> chế độ"}, "deleteMode": "<PERSON><PERSON><PERSON> ch<PERSON> độ"}, "allFiles": "tất c<PERSON> các tệp"}