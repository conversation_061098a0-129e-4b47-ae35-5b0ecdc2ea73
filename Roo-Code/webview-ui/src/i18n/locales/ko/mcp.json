{"title": "MCP 서버", "done": "완료", "description": "Model Context Protocol(MCP)를 활성화하면 Roo Code가 외부 서버에서 추가 도구와 서비스를 사용할 수 있어. Roo가 할 수 있는 일이 더 많아져! <0>자세히 알아보기</0>", "enableToggle": {"title": "MCP 서버 활성화", "description": "이걸 켜면 Roo가 연결된 MCP 서버의 도구를 쓸 수 있어. Roo의 능력이 더 늘어나! 추가 도구를 쓸 생각이 없다면, API 토큰 비용을 줄이기 위해 꺼 두는 게 좋아."}, "enableServerCreation": {"title": "MCP 서버 생성 활성화", "description": "이걸 켜면 Roo가 <1>새로운</1> 맞춤형 MCP 서버를 만드는 걸 도와줄 수 있어. <0>서버 생성에 대해 알아보기</0>", "hint": "팁: API 토큰 비용을 줄이고 싶으면, Roo에게 새 MCP 서버를 만들라고 하지 않을 때 이 설정을 꺼 둬."}, "editGlobalMCP": "글로벌 MCP 편집", "editProjectMCP": "프로젝트 MCP 편집", "learnMoreEditingSettings": "MCP 설정 파일 편집 방법 더 알아보기", "tool": {"alwaysAllow": "항상 허용", "parameters": "파라미터", "noDescription": "설명 없음"}, "tabs": {"tools": "도구", "resources": "리소스", "errors": "오류"}, "emptyState": {"noTools": "도구를 찾을 수 없음", "noResources": "리소스를 찾을 수 없음", "noLogs": "로그를 찾을 수 없음", "noErrors": "오류를 찾을 수 없음"}, "networkTimeout": {"label": "네트워크 타임아웃", "description": "서버 응답을 기다리는 최대 시간", "options": {"15seconds": "15초", "30seconds": "30초", "1minute": "1분", "5minutes": "5분", "10minutes": "10분", "15minutes": "15분", "30minutes": "30분", "60minutes": "60분"}}, "deleteDialog": {"title": "MCP 서버 삭제", "description": "정말로 MCP 서버 \"{{serverName}}\"을(를) 삭제할까? 이 작업은 되돌릴 수 없어.", "cancel": "취소", "delete": "삭제"}, "serverStatus": {"retrying": "다시 시도 중...", "retryConnection": "연결 다시 시도"}}