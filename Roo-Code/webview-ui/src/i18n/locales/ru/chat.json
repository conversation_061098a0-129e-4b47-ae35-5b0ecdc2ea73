{"greeting": "Добро пожаловать в Roo Code", "task": {"title": "Задача", "seeMore": "Показать больше", "seeLess": "Показать меньше", "tokens": "Токенов:", "cache": "Кэш:", "apiCost": "Стоимость API:", "contextWindow": "<PERSON><PERSON><PERSON><PERSON> контекста:", "closeAndStart": "Закрыть задачу и начать новую", "export": "Экспортировать историю задач", "delete": "Удалить задачу (Shift + клик для пропуска подтверждения)", "condenseContext": "Сжать контекст задачи"}, "unpin": "Открепить", "pin": "Закрепить", "retry": {"title": "Повторить", "tooltip": "Попробовать выполнить операцию снова"}, "startNewTask": {"title": "Начать новую задачу", "tooltip": "Начать новую задачу"}, "proceedAnyways": {"title": "Все равно продолжить", "tooltip": "Продолжить выполнение команды"}, "save": {"title": "Сохранить", "tooltip": "Сохранить изменения в файле"}, "tokenProgress": {"availableSpace": "Доступно места: {{amount}} токенов", "tokensUsed": "Использовано токенов: {{used}} из {{total}}", "reservedForResponse": "Зарезервировано для ответа модели: {{amount}} токенов"}, "reject": {"title": "Отклонить", "tooltip": "Отклонить это действие"}, "completeSubtaskAndReturn": "Завершить подзадачу и вернуться", "approve": {"title": "Одобрить", "tooltip": "Одобрить это действие"}, "runCommand": {"title": "Выполнить команду", "tooltip": "Выполнить эту команду"}, "proceedWhileRunning": {"title": "Продолжить во время выполнения", "tooltip": "Продолжить несмотря на предупреждения"}, "resumeTask": {"title": "Возобновить задачу", "tooltip": "Продолжить текущую задачу"}, "killCommand": {"title": "Завер<PERSON>ить команду", "tooltip": "Завершить текущую команду"}, "terminate": {"title": "Завершить", "tooltip": "Завершить текущую задачу"}, "cancel": {"title": "Отмена", "tooltip": "Отменить текущую операцию"}, "scrollToBottom": "Прокрутить чат вниз", "about": "Создавайте, рефакторите и отлаживайте код с помощью ИИ.<br />Подробнее см. в нашей <DocsLink>документации</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Задачи-бумеранги", "description": "Разделяйте задачи на более мелкие, управляемые части"}, "stickyModels": {"title": "Липкие режимы", "description": "Каждый режим запоминает вашу последнюю использованную модель"}, "tools": {"title": "Инструменты", "description": "Разрешите ИИ решать проблемы, просматривая веб-страницы, выполняя команды и т. д."}, "customizableModes": {"title": "Настраиваемые режимы", "description": "Специализированные персонажи с собственным поведением и назначенными моделями"}}, "onboarding": "<strong>Ваш список задач в этом рабочем пространстве пуст.</strong> Начните с ввода задачи ниже. Не знаете, с чего начать? Подробнее о возможностях Roo читайте в <DocsLink>документации</DocsLink>.", "selectMode": "Выберите режим взаимодействия", "selectApiConfig": "Выберите конфигурацию API", "enhancePrompt": "Улучшить запрос с дополнительным контекстом", "enhancePromptDescription": "Кнопка 'Улучшить запрос' помогает сделать ваш запрос лучше, предоставляя дополнительный контекст, уточнения или переформулировку. Попробуйте ввести запрос и снова нажать кнопку, чтобы увидеть, как это работает.", "addImages": "Добавить изображения к сообщению", "sendMessage": "Отправить сообщение", "typeMessage": "Введите сообщение...", "typeTask": "Введите вашу задачу здесь...", "addContext": "@ для добавления контекста, / для смены режима", "dragFiles": "удерживайте shift для перетаскивания файлов", "dragFilesImages": "удерживайте shift для перетаскивания файлов/изображений", "errorReadingFile": "Ошибка чтения файла:", "noValidImages": "Не удалось обработать ни одно изображение", "separator": "Разделитель", "edit": "Редактировать...", "forNextMode": "для следующего режима", "apiRequest": {"title": "API-запрос", "failed": "API-запрос не выполнен", "streaming": "API-запрос...", "cancelled": "API-запрос отменен", "streamingFailed": "Ошибка потокового API-запроса"}, "checkpoint": {"initial": "Начальная точка сохранения", "regular": "Точка сохранения", "initializingWarning": "Точка сохранения еще инициализируется... Если это занимает слишком много времени, вы можете отключить точки сохранения в <settingsLink>настройках</settingsLink> и перезапустить задачу.", "menu": {"viewDiff": "Просмотреть различия", "restore": "Восстановить точку сохранения", "restoreFiles": "Восстановить файлы", "restoreFilesDescription": "Восстанавливает файлы вашего проекта до состояния на момент этой точки.", "restoreFilesAndTask": "Восстановить файлы и задачу", "confirm": "Подтвердить", "cancel": "Отмена", "cannotUndo": "Это действие нельзя отменить.", "restoreFilesAndTaskDescription": "Восстанавливает файлы проекта до состояния на момент этой точки и удаляет все сообщения после нее."}, "current": "Текущая"}, "instructions": {"wantsToFetch": "Roo хочет получить подробные инструкции для помощи с текущей задачей"}, "fileOperations": {"wantsToRead": "Roo хочет прочитать этот файл:", "wantsToReadOutsideWorkspace": "Roo хочет прочитать этот файл вне рабочей области:", "didRead": "Roo прочитал этот файл:", "wantsToEdit": "Roo хочет отредактировать этот файл:", "wantsToEditOutsideWorkspace": "Roo хочет отредактировать этот файл вне рабочей области:", "wantsToCreate": "Roo хочет создать новый файл:", "wantsToSearchReplace": "Roo хочет выполнить поиск и замену в этом файле:", "didSearchReplace": "Roo выполнил поиск и замену в этом файле:", "wantsToInsert": "Roo хочет вставить содержимое в этот файл:", "wantsToInsertWithLineNumber": "Roo хочет вставить содержимое в этот файл на строку {{lineNumber}}:", "wantsToInsertAtEnd": "Roo хочет добавить содержимое в конец этого файла:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo хочет просмотреть файлы верхнего уровня в этой директории:", "didViewTopLevel": "Roo просмотрел файлы верхнего уровня в этой директории:", "wantsToViewRecursive": "Roo хочет рекурсивно просмотреть все файлы в этой директории:", "didViewRecursive": "Roo рекурсивно просмотрел все файлы в этой директории:", "wantsToViewDefinitions": "Roo хочет просмотреть имена определений исходного кода в этой директории:", "didViewDefinitions": "Roo просмотрел имена определений исходного кода в этой директории:", "wantsToSearch": "Roo хочет выполнить поиск в этой директории по <code>{{regex}}</code>:", "didSearch": "Roo выполнил поиск в этой директории по <code>{{regex}}</code>:"}, "commandOutput": "Вывод команды", "response": "Ответ", "arguments": "Аргументы", "mcp": {"wantsToUseTool": "Roo хочет использовать инструмент на сервере MCP {{serverName}}:", "wantsToAccessResource": "Roo хочет получить доступ к ресурсу на сервере MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Roo хочет переключиться в режим {{mode}}", "wantsToSwitchWithReason": "Roo хочет переключиться в режим {{mode}}, потому что: {{reason}}", "didSwitch": "Roo переключился в режим {{mode}}", "didSwitchWithReason": "Roo переключился в режим {{mode}}, потому что: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo хочет создать новую подзадачу в режиме {{mode}}:", "wantsToFinish": "Roo хочет завершить эту подзадачу", "newTaskContent": "Инструкции по подзадаче", "completionContent": "Подзадача завершена", "resultContent": "Результаты подзадачи", "defaultResult": "Пожалуйста, переходите к следующей задаче.", "completionInstructions": "Подзадача завершена! Вы можете просмотреть результаты и предложить исправления или следующие шаги. Если всё в порядке, подтвердите для возврата результата в родительскую задачу."}, "questions": {"hasQuestion": "У Roo есть вопрос:"}, "taskCompleted": "Задача завершена", "error": "Ошибка", "diffError": {"title": "Не удалось выполнить редактирование"}, "troubleMessage": "У Roo возникли проблемы...", "powershell": {"issues": "Похоже, у вас проблемы с Windows PowerShell, пожалуйста, ознакомьтесь с этим"}, "autoApprove": {"title": "Автоодобрение:", "none": "Нет", "description": "Автоодобрение позволяет Roo Code выполнять действия без запроса разрешения. Включайте только для полностью доверенных действий. Более подробная настройка доступна в <settingsLink>Настройках</settingsLink>."}, "announcement": {"title": "🎉 Выпущен Roo Code {{version}}", "description": "Roo Code {{version}} приносит мощные новые функции и улучшения на основе ваших отзывов.", "whatsNew": "Что нового", "feature1": "<bold>Неявное кэширование для Gemini</bold>: Вызовы API Gemini теперь автоматически кэшируются, сокращая затраты на API", "feature2": "<bold>Умнее выбор режимов</bold>: Определения режимов теперь могут включать указания о том, когда следует использовать каждый режим, обеспечивая лучшую оркестрацию", "feature3": "<bold>Интеллектуальное сжатие контекста</bold>: Интеллектуально обобщает историю разговоров, когда контекст заполняется, вместо усечения (включите в Настройки -> Экспериментальные)", "hideButton": "Скрыть объявление", "detailsDiscussLinks": "Подробнее и обсуждение в <discordLink>Discord</discordLink> и <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Обдумывание", "seconds": "{{count}}с"}, "contextCondense": {"title": "Контекст сжат", "condensing": "Сжатие контекста...", "tokens": "токены"}, "followUpSuggest": {"copyToInput": "Скопировать во ввод (то же, что shift + клик)"}, "browser": {"rooWantsToUse": "Roo хочет использовать браузер:", "consoleLogs": "Логи консоли", "noNewLogs": "(Новых логов нет)", "screenshot": "Скриншот браузера", "cursor": "курсор", "navigation": {"step": "Шаг {{current}} из {{total}}", "previous": "Предыдущий", "next": "Следующий"}, "sessionStarted": "Сессия браузера запущена", "actions": {"title": "Действие в браузере: ", "launch": "Открыть браузер по адресу {{url}}", "click": "Клик ({{coordinate}})", "type": "Ввести \"{{text}}\"", "scrollDown": "Прокрутить вниз", "scrollUp": "Прокрутить вверх", "close": "Закрыть браузер"}}, "codeblock": {"tooltips": {"expand": "Развернуть блок кода", "collapse": "Свернуть блок кода", "enable_wrap": "Включить перенос строк", "disable_wrap": "Отключить перенос строк", "copy_code": "Копировать код"}}, "systemPromptWarning": "ПРЕДУПРЕЖДЕНИЕ: Активна пользовательская системная подсказка. Это может серьезно нарушить работу и вызвать непредсказуемое поведение.", "shellIntegration": {"title": "Предупреждение о выполнении команды", "description": "Ваша команда выполняется без интеграции оболочки терминала VSCode. Чтобы скрыть это предупреждение, вы можете отключить интеграцию оболочки в разделе <strong>Terminal</strong> в <settingsLink>настройках Roo Code</settingsLink> или устранить проблемы с интеграцией терминала VSCode, используя ссылку ниже.", "troubleshooting": "Нажмите здесь для просмотра документации по интеграции оболочки."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Достигнут лимит автоматически одобренных запросов", "description": "Roo достиг автоматически одобренного лимита в {{count}} API-запрос(ов). Хотите сбросить счетчик и продолжить задачу?", "button": "Сбросить и продолжить"}}}