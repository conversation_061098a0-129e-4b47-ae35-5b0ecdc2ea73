{"title": "Prompts", "done": "<PERSON><PERSON><PERSON>", "modes": {"title": "<PERSON><PERSON>", "createNewMode": "Neuen Modus erstellen", "editModesConfig": "Moduskonfiguration bearbeiten", "editGlobalModes": "Globale Modi bear<PERSON>ten", "editProjectModes": "<PERSON>jekt<PERSON><PERSON> bearbeiten (.room<PERSON>)", "createModeHelpText": "Modi sind spezialisierte Personas, die Roos Verhalten anpassen. <0>Erfahre mehr über die Verwendung von Modi</0> oder <1>die Anpassung von Modi.</1>", "selectMode": "<PERSON><PERSON>"}, "apiConfiguration": {"title": "API-Konfiguration", "select": "<PERSON><PERSON><PERSON><PERSON>, welche API-Konfiguration für diesen Modus verwendet werden soll"}, "tools": {"title": "Verfügbare Werkzeuge", "builtInModesText": "Werkzeuge für eingebaute Modi können nicht geändert werden", "editTools": "Werkzeuge bearbeiten", "doneEditing": "Bearbeitung abschließen", "allowedFiles": "Erlaubte Dateien:", "toolNames": {"read": "<PERSON><PERSON>", "edit": "<PERSON><PERSON> bear<PERSON>", "browser": "Browser verwenden", "command": "Befehle ausführen", "mcp": "MCP verwenden"}, "noTools": "<PERSON><PERSON>"}, "roleDefinition": {"title": "Rollendefinition", "resetToDefault": "Auf Standardwerte zurücksetzen", "description": "Definiere Roos Expertise und Persönlichkeit für diesen Modus. Diese Beschreibung prägt, wie Roo sich präsentiert und an Aufgaben herangeht."}, "whenToUse": {"title": "<PERSON><PERSON> zu verwenden (optional)", "description": "<PERSON><PERSON><PERSON><PERSON>, wann dieser Modus verwendet werden sollte. Die<PERSON> hilft dem Orchestrator, den richtigen Modus für eine Aufgabe auszuwählen.", "resetToDefault": "Beschreibung 'Wann zu verwenden' auf Standardwerte zurücksetzen"}, "customInstructions": {"title": "Modusspezifische benutzerdefinierte Anweisungen (optional)", "resetToDefault": "Auf Standardwerte zurücksetzen", "description": "Fügen Sie verhaltensspezifische Richtlinien für den Modus {{modeName}} hinzu.", "loadFromFile": "Benutzerdefinierte Anweisungen für den Modus {{mode}} können auch aus dem Ordner <span>.roo/rules-{{slug}}/</span> in deinem Arbeitsbereich geladen werden (.roorules-{{slug}} und .clinerules-{{slug}} sind veraltet und werden bald nicht mehr funktionieren)."}, "globalCustomInstructions": {"title": "Benutzerdefinierte Anweisungen für alle Modi", "description": "Diese Anweisungen gelten für alle Modi. Sie bieten einen grundlegenden Satz von Verhaltensweisen, die durch modusspezifische Anweisungen unten erweitert werden können. <0><PERSON>hr erfahren</0>", "loadFromFile": "Anweisungen können auch aus dem Ordner <span>.roo/rules/</span> in deinem Arbeitsbereich geladen werden (.roorules und .clinerules sind veraltet und werden bald nicht mehr funktionieren)."}, "systemPrompt": {"preview": "System-Prompt Vorschau", "copy": "System-Prompt in Zwischenablage kopieren", "title": "System-Prompt (Modus {{modeName}})"}, "supportPrompts": {"title": "Support-Prompts", "resetPrompt": "{{promptType}}-Prompt auf Standardwerte zurücksetzen", "prompt": "Prompt", "enhance": {"apiConfiguration": "API-Konfiguration", "apiConfigDescription": "Du kannst eine API-Konfiguration auswählen, die immer zur Verbesserung von Prompts verwendet wird, oder einfach die aktuell ausgewählte verwenden", "useCurrentConfig": "Aktuell ausgewählte API-Konfiguration verwenden", "testPromptPlaceholder": "Gib einen Prompt ein, um die Verbesserung zu testen", "previewButton": "Vorschau der Prompt-Verbesserung"}, "types": {"ENHANCE": {"label": "Prompt verbessern", "description": "Verwenden Sie die Prompt-Verbesserung, um maßgeschneiderte Vorschläge oder Verbesserungen für Ihre Eingaben zu erhalten. Dies stellt sicher, dass Roo Ihre Absicht versteht und die bestmöglichen Antworten liefert. Verfügbar über das ✨-Symbol im Chat."}, "EXPLAIN": {"label": "Code erklären", "description": "Erhalten Sie detaillierte Erklärungen zu Code-<PERSON>hn<PERSON>n, Funktionen oder ganzen Dateien. Nützlich zum Verständnis komplexen Codes oder zum Erlernen neuer Muster. Verfügbar in Code-Aktionen (Glühbirnen-Symbol im Editor) und im Kontextmenü des Editors (Rechtsklick auf ausgewählten Code)."}, "FIX": {"label": "<PERSON>e beheben", "description": "Erhalten Sie Hilfe beim Identifizieren und <PERSON><PERSON><PERSON>, Fehlern oder Problemen mit der Code-Qualität. Bietet Schritt-für-Schritt-Anleitungen zur Problemlösung. Verfügbar in Code-Aktionen (Glühbirnen-Symbol im Editor) und im Kontextmenü des Editors (Rechtsklick auf ausgewählten Code)."}, "IMPROVE": {"label": "Code verbessern", "description": "Erhalten Sie Vorschläge zur Code-Optimierung, bessere Praktiken und architektonische Verbesserungen bei gleichzeitiger Beibehaltung der Funktionalität. Verfügbar in Code-Aktionen (Glühbirnen-Symbol im Editor) und im Kontextmenü des Editors (Rechtsklick auf ausgewählten Code)."}, "ADD_TO_CONTEXT": {"label": "Zum Kontext hinzufügen", "description": "Fügen Sie Kontext zu Ihrer aktuellen Aufgabe oder Konversation hinzu. Nützlich für die Bereitstellung zusätzlicher Informationen oder Klarstellungen. Verfügbar in Code-Aktionen (Glühbirnen-Symbol im Editor) und im Kontextmenü des Editors (Rechtsklick auf ausgewählten Code)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Terminal-Inhalt zum Kontext hinzufügen", "description": "Fügen Sie Terminal-Ausgaben zu Ihrer aktuellen Aufgabe oder Konversation hinzu. Nützlich für die Bereitstellung von Befehlsausgaben oder Protokollen. Verfügbar im Kontextmenü des Terminals (Rechtsklick auf ausgewählten Terminal-Inhalt)."}, "TERMINAL_FIX": {"label": "Terminal-Befehl korrigieren", "description": "Erhalten Sie Hilfe bei der Korrektur fehlgeschlagener Terminal-Befehle oder solcher, die Verbesserungen benötigen. Verfügbar im Kontextmenü des Terminals (Rechtsklick auf ausgewählten Terminal-Inhalt)."}, "TERMINAL_EXPLAIN": {"label": "Terminal-Befehl erklären", "description": "Erhalten Sie detaillierte Erklärungen zu Terminal-Befehlen und deren Ausgaben. Verfügbar im Kontextmenü des Terminals (Rechtsklick auf ausgewählten Terminal-Inhalt)."}, "NEW_TASK": {"label": "Neue Aufgabe starten", "description": "Starte eine neue Aufgabe mit deiner Eingabe. Verfügbar in der Befehlspalette."}}}, "advancedSystemPrompt": {"title": "Erweitert: System-Prompt überschreiben", "description": "<2>⚠️ Warnung:</2> Diese erweiterte Funktion umgeht Sicherheitsvorkehrungen. <1>LESEN SIE DIES VOR DER VERWENDUNG!</1>Überschreiben Sie den Standard-System-Prompt, indem Sie eine Datei unter <span>.roo/system-prompt-{{slug}}</span> erstellen."}, "createModeDialog": {"title": "Neuen Modus erstellen", "close": "Schließen", "name": {"label": "Name", "placeholder": "Modusnamen e<PERSON>ben"}, "slug": {"label": "Slug", "description": "Der Slug wird in URLs und Dateinamen verwendet. Er sollte in Kleinbuchstaben sein und nur Buchstaben, Zahlen und Bindestriche enthalten."}, "saveLocation": {"label": "Speicherort", "description": "<PERSON><PERSON><PERSON><PERSON>, wo dieser Modus gespeichert werden soll. Projektspezifische Modi haben Vorrang vor globalen Modi.", "global": {"label": "Global", "description": "Verfügbar in allen Arbeitsbereichen"}, "project": {"label": "Projektspezifisch (.roomodes)", "description": "Nur in diesem Arbeitsbereich verfügbar, hat Vorrang vor global"}}, "roleDefinition": {"label": "Rollendefinition", "description": "Definiere Roos Expertise und Persönlichkeit für diesen Modus."}, "whenToUse": {"label": "<PERSON><PERSON> zu verwenden (optional)", "description": "Gib eine klare Beschreibung, wann dieser Modus am effektivsten ist und für welche Arten von Aufgaben er sich besonders eignet."}, "tools": {"label": "Verfügbare Werkzeuge", "description": "<PERSON><PERSON><PERSON><PERSON>, welche Werkzeuge dieser Modus verwenden kann."}, "customInstructions": {"label": "Benutzerdefinierte Anweisungen (optional)", "description": "Fügen Sie verhaltensspezifische Richtlinien für diesen Modus hinzu."}, "buttons": {"cancel": "Abbrechen", "create": "<PERSON><PERSON> er<PERSON>llen"}, "deleteMode": "Modus <PERSON>"}, "allFiles": "alle Dateien"}