{"title": "Servidores MCP", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ative o Model Context Protocol (MCP) para permitir que o Roo Code use ferramentas e serviços extras de servidores externos. Isso amplia o que o Roo pode fazer por você. <0><PERSON><PERSON> mais</0>", "enableToggle": {"title": "Ativar servidores MCP", "description": "Ative para que o Roo possa usar ferramentas de servidores MCP conectados. Isso dá mais capacidades ao Roo. Se você não pretende usar essas ferramentas extras, desative para ajudar a reduzir os custos de tokens da API."}, "enableServerCreation": {"title": "Ativar criação de servidores MCP", "description": "Ative para que o Roo possa te ajudar a criar <1>novos</1> servidores MCP personalizados. <0>Saiba mais sobre criação de servidores</0>", "hint": "Dica: Para reduzir os custos de tokens da API, desative esta configuração quando não estiver pedindo ao Roo para criar um novo servidor MCP."}, "editGlobalMCP": "Editar MCP global", "editProjectMCP": "Editar MCP do projeto", "learnMoreEditingSettings": "Saiba mais sobre como editar arquivos de configuração MCP", "tool": {"alwaysAllow": "Sempre permitir", "parameters": "Parâmetros", "noDescription": "Sem descrição"}, "tabs": {"tools": "Ferramentas", "resources": "Recursos", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON>enhuma ferramenta encontrada", "noResources": "Nenhum recurso encontrado", "noLogs": "Nenhum log encontrado", "noErrors": "Nenhum erro encontrado"}, "networkTimeout": {"label": "Tempo limite de rede", "description": "Tempo máximo de espera por respostas do servidor", "options": {"15seconds": "15 segundos", "30seconds": "30 segundos", "1minute": "1 minuto", "5minutes": "5 minutos", "10minutes": "10 minutos", "15minutes": "15 minutos", "30minutes": "30 minutos", "60minutes": "60 minutos"}}, "deleteDialog": {"title": "Excluir servidor MCP", "description": "Tem certeza de que deseja excluir o servidor MCP \"{{serverName}}\"? Esta ação não pode ser desfeita.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir"}, "serverStatus": {"retrying": "Tentando novamente...", "retryConnection": "Tentar reconectar"}}