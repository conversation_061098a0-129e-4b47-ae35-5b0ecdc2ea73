{"title": "Solicitudes", "done": "Listo", "modes": {"title": "Modos", "createNewMode": "Crear nuevo modo", "editModesConfig": "Editar configuración de modos", "editGlobalModes": "Editar modos globales", "editProjectModes": "Editar modos del proyecto (.roomodes)", "createModeHelpText": "Los modos son personas especializadas que adaptan el comportamiento de Roo. <0>Aprende sobre el uso de modos</0> o <1>Personalización de modos.</1>", "selectMode": "Buscar modos"}, "apiConfiguration": {"title": "Configuración de API", "select": "Selecciona qué configuración de API usar para este modo"}, "tools": {"title": "Herramientas disponibles", "builtInModesText": "Las herramientas para modos integrados no se pueden modificar", "editTools": "<PERSON><PERSON>", "doneEditing": "Terminar edición", "allowedFiles": "Archivos permitidos:", "toolNames": {"read": "Leer archivos", "edit": "Editar archivos", "browser": "<PERSON><PERSON>", "command": "Ejecutar comandos", "mcp": "Usar MCP"}, "noTools": "Ninguna"}, "roleDefinition": {"title": "Definición de rol", "resetToDefault": "Restablecer a valores predeterminados", "description": "Define la experiencia y personalidad de Roo para este modo. Esta descripción determina cómo Roo se presenta y aborda las tareas."}, "whenToUse": {"title": "<PERSON><PERSON><PERSON><PERSON> usar (opcional)", "description": "Describe cuándo se debe usar este modo. Esto ayuda al Orchestrator a elegir el modo correcto para una tarea.", "resetToDefault": "Restablecer a valores predeterminados la descripción 'Cuándo usar'"}, "customInstructions": {"title": "Instrucciones personalizadas para el modo (opcional)", "resetToDefault": "Restablecer a valores predeterminados", "description": "Agrega directrices de comportamiento específicas para el modo {{modeName}}.", "loadFromFile": "Las instrucciones personalizadas para el modo {{mode}} tamb<PERSON><PERSON> se pueden cargar desde la carpeta <span>.roo/rules-{{slug}}/</span> en tu espacio de trabajo (.roorules-{{slug}} y .clinerules-{{slug}} están obsoletos y dejarán de funcionar pronto)."}, "globalCustomInstructions": {"title": "Instrucciones personalizadas para todos los modos", "description": "Estas instrucciones se aplican a todos los modos. Proporcionan un conjunto base de comportamientos que pueden ser mejorados por instrucciones específicas de cada modo a continuación. <0>Más información</0>", "loadFromFile": "Las instrucciones también se pueden cargar desde la carpeta <span>.roo/rules/</span> en tu espacio de trabajo (.roorules y .clinerules están obsoletos y dejarán de funcionar pronto)."}, "systemPrompt": {"preview": "Vista previa de la solicitud del sistema", "copy": "Copiar solicitud del sistema al portapapeles", "title": "Solicitud del sistema (modo {{modeName}})"}, "supportPrompts": {"title": "Solicitudes de soporte", "resetPrompt": "Restablecer la solicitud {{promptType}} a valores predeterminados", "prompt": "Solicitud", "enhance": {"apiConfiguration": "Configuración de API", "apiConfigDescription": "Puedes seleccionar una configuración de API para usar siempre en la mejora de solicitudes, o simplemente usar la que esté seleccionada actualmente", "useCurrentConfig": "Usar la configuración de API actualmente seleccionada", "testPromptPlaceholder": "Ingresa una solicitud para probar la mejora", "previewButton": "Vista previa de la mejora de solicitud"}, "types": {"ENHANCE": {"label": "<PERSON><PERSON><PERSON> solicitud", "description": "Utiliza la mejora de solicitudes para obtener sugerencias o mejoras personalizadas para tus entradas. Esto asegura que Roo entienda tu intención y proporcione las mejores respuestas posibles. Disponible a través del icono ✨ en el chat."}, "EXPLAIN": {"label": "Explicar código", "description": "Obtén explicaciones detalladas de fragmentos de código, funciones o archivos completos. Útil para entender código complejo o aprender nuevos patrones. Disponible en acciones de código (icono de bombilla en el editor) y en el menú contextual del editor (clic derecho en el código seleccionado)."}, "FIX": {"label": "Corre<PERSON>r problemas", "description": "Obtén ayuda para identificar y resolver errores, fallos o problemas de calidad del código. Proporciona una guía paso a paso para solucionar problemas. Disponible en acciones de código (icono de bombilla en el editor) y en el menú contextual del editor (clic derecho en el código seleccionado)."}, "IMPROVE": {"label": "<PERSON><PERSON><PERSON> c<PERSON>", "description": "Recibe sugerencias para optimización de código, mejores prácticas y mejoras arquitectónicas manteniendo la funcionalidad. Disponible en acciones de código (icono de bombilla en el editor) y en el menú contextual del editor (clic derecho en el código seleccionado)."}, "ADD_TO_CONTEXT": {"label": "<PERSON><PERSON><PERSON> al <PERSON>", "description": "Añade contexto a tu tarea o conversación actual. Útil para proporcionar información adicional o aclaraciones. Disponible en acciones de código (icono de bombilla en el editor) y en el menú contextual del editor (clic derecho en el código seleccionado)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Añadir contenido de terminal al contexto", "description": "Añade la salida de la terminal a tu tarea o conversación actual. Útil para proporcionar salidas de comandos o registros. Disponible en el menú contextual de la terminal (clic derecho en el contenido seleccionado de la terminal)."}, "TERMINAL_FIX": {"label": "Corregir comando de terminal", "description": "Obtén ayuda para corregir comandos de terminal que fallaron o necesitan mejoras. Disponible en el menú contextual de la terminal (clic derecho en el contenido seleccionado de la terminal)."}, "TERMINAL_EXPLAIN": {"label": "Explicar comando de terminal", "description": "Obtén explicaciones detalladas de comandos de terminal y sus salidas. Disponible en el menú contextual de la terminal (clic derecho en el contenido seleccionado de la terminal)."}, "NEW_TASK": {"label": "Iniciar nueva tarea", "description": "Inicia una nueva tarea con entrada del usuario. Disponible en la Paleta de comandos."}}}, "advancedSystemPrompt": {"title": "Avanzado: <PERSON><PERSON> solicitud del sistema", "description": "<2>⚠️ Advertencia:</2> Esta función avanzada omite las medidas de seguridad. <1>¡LEE ESTO ANTES DE USAR!</1>Anula la solicitud del sistema predeterminada creando un archivo en <span>.roo/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Crear nuevo modo", "close": "<PERSON><PERSON><PERSON>", "name": {"label": "Nombre", "placeholder": "Ingresa nombre del modo"}, "slug": {"label": "Slug", "description": "El slug se usa en URLs y nombres de archivos. Debe estar en minúscula y contener solo letras, números y guiones."}, "saveLocation": {"label": "Ubicación para guardar", "description": "Elige dónde guardar este modo. Los modos específicos del proyecto tienen prioridad sobre los modos globales.", "global": {"label": "Global", "description": "Disponible en todos los espacios de trabajo"}, "project": {"label": "Específico del proyecto (.roomodes)", "description": "Solo disponible en este espacio de trabajo, tiene prioridad sobre el global"}}, "roleDefinition": {"label": "Definición de rol", "description": "Define la experiencia y personalidad de Roo para este modo."}, "whenToUse": {"label": "<PERSON><PERSON><PERSON><PERSON> usar (opcional)", "description": "Proporciona una descripción clara de cuándo este modo es más efectivo y para qué tipos de tareas es más adecuado."}, "tools": {"label": "Herramientas disponibles", "description": "Selecciona qué herramientas puede usar este modo."}, "customInstructions": {"label": "Instrucciones personalizadas (opcional)", "description": "Agrega directrices de comportamiento específicas para este modo."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON>rear modo"}, "deleteMode": "Eliminar modo"}, "allFiles": "todos los archivos"}