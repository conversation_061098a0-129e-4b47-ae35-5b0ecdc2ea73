{"greeting": "Bienvenido a Roo Code", "task": {"title": "Tarea", "seeMore": "<PERSON>er más", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Caché:", "apiCost": "Costo de API:", "contextWindow": "Longitud del contexto:", "closeAndStart": "Cerrar tarea e iniciar una nueva", "export": "Exportar historial de tareas", "delete": "Eliminar tarea (Shift + Clic para omitir confirmación)", "condenseContext": "Condensar contexto de la tarea"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "<PERSON><PERSON>", "retry": {"title": "Reintentar", "tooltip": "Intenta la operación de nuevo"}, "startNewTask": {"title": "Iniciar nueva tarea", "tooltip": "Comienza una nueva tarea"}, "proceedAnyways": {"title": "Con<PERSON><PERSON><PERSON> de todos modos", "tooltip": "Continuar mientras se ejecuta el comando"}, "save": {"title": "Guardar", "tooltip": "Guardar los cambios del archivo"}, "tokenProgress": {"availableSpace": "Espacio disponible: {{amount}} tokens", "tokensUsed": "Tokens utilizados: {{used}} de {{total}}", "reservedForResponse": "Reservado para respuesta del modelo: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Re<PERSON>zar esta acción"}, "completeSubtaskAndReturn": "Completar subtarea y regresar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprobar esta acción"}, "runCommand": {"title": "Ejecutar comando", "tooltip": "Ejecutar este comando"}, "proceedWhileRunning": {"title": "Continuar mientras se ejecuta", "tooltip": "Continuar a pesar de las advertencias"}, "killCommand": {"title": "Terminar comando", "tooltip": "Terminar el comando actual"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> la tarea actual"}, "terminate": {"title": "Terminar", "tooltip": "Terminar la tarea actual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar la operación actual"}, "scrollToBottom": "Desplazarse al final del chat", "about": "Genera, refactoriza y depura código con asistencia de IA.<br />Consulta nuestra <DocsLink>documentación</DocsLink> para obtener más información.", "onboarding": "<strong>Tu lista de tareas en este espacio de trabajo está vacía.</strong> Comienza escribiendo una tarea abajo. ¿No estás seguro cómo empezar? Lee más sobre lo que Roo puede hacer por ti en <DocsLink>la documentación</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "<PERSON><PERSON><PERSON>", "description": "Divide las tareas en partes más pequeñas y manejables."}, "stickyModels": {"title": "Modos persistentes", "description": "Cada modo recuerda tu último modelo utilizado"}, "tools": {"title": "Herramientas", "description": "Permite que la IA resuelva problemas navegando por la web, ejecutando comandos y mucho más."}, "customizableModes": {"title": "Modos personalizables", "description": "Personalidades especializadas con sus propios comportamientos y modelos asignados"}}, "selectMode": "Seleccionar modo de interacción", "selectApiConfig": "Seleccionar configuración de API", "enhancePrompt": "Mejorar el mensaje con contexto adicional", "addImages": "Agregar imágenes al mensaje", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "typeMessage": "Escribe un mensaje...", "typeTask": "Escribe tu tarea aquí...", "addContext": "@ para agregar contexto, / para cambiar modos", "dragFiles": "mantén shift para arrastrar archivos", "dragFilesImages": "mantén shift para arrastrar archivos/imágenes", "enhancePromptDescription": "El botón 'Mejorar el mensaje' ayuda a mejorar tu petición proporcionando contexto adicional, aclaraciones o reformulaciones. Intenta escribir una petición aquí y haz clic en el botón nuevamente para ver cómo funciona.", "errorReadingFile": "Error al leer el archivo:", "noValidImages": "No se procesaron imágenes válidas", "separator": "Separador", "edit": "Editar...", "forNextMode": "para el siguiente modo", "error": "Error", "diffError": {"title": "Edición fallida"}, "troubleMessage": "Roo está teniendo problemas...", "apiRequest": {"title": "Solicitud API", "failed": "Solicitud API falló", "streaming": "Solicitud API...", "cancelled": "Solicitud API cancelada", "streamingFailed": "Transmisión API falló"}, "checkpoint": {"initial": "Punto de control inicial", "regular": "Punto de control", "initializingWarning": "Todavía inicializando el punto de control... Si esto tarda demasiado, puedes desactivar los puntos de control en la <settingsLink>configuración</settingsLink> y reiniciar tu tarea.", "menu": {"viewDiff": "Ver diferencias", "restore": "Restaurar punto de control", "restoreFiles": "Restaurar archivos", "restoreFilesDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto.", "restoreFilesAndTask": "Restaurar archivos y tarea", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta acción no se puede deshacer.", "restoreFilesAndTaskDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto y elimina todos los mensajes posteriores a este punto."}, "current": "Actual"}, "instructions": {"wantsToFetch": "Roo quiere obtener instrucciones detalladas para ayudar con la tarea actual"}, "fileOperations": {"wantsToRead": "<PERSON>oo quiere leer este archivo:", "wantsToReadOutsideWorkspace": "<PERSON>oo quiere leer este archivo fuera del espacio de trabajo:", "didRead": "<PERSON><PERSON> leyó este archivo:", "wantsToEdit": "<PERSON>oo quiere editar este archivo:", "wantsToEditOutsideWorkspace": "<PERSON>oo quiere editar este archivo fuera del espacio de trabajo:", "wantsToCreate": "Roo quiere crear un nuevo archivo:", "wantsToSearchReplace": "<PERSON>oo quiere realizar b<PERSON>queda y reemplazo en este archivo:", "didSearchReplace": "Roo realizó búsqueda y reemplazo en este archivo:", "wantsToInsert": "<PERSON>oo quiere insertar contenido en este archivo:", "wantsToInsertWithLineNumber": "<PERSON>oo quiere insertar contenido en este archivo en la línea {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON><PERSON> quiere añadir contenido al final de este archivo:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo quiere ver los archivos de nivel superior en este directorio:", "didViewTopLevel": "Roo vio los archivos de nivel superior en este directorio:", "wantsToViewRecursive": "Roo quiere ver recursivamente todos los archivos en este directorio:", "didViewRecursive": "Roo vio recursivamente todos los archivos en este directorio:", "wantsToViewDefinitions": "Roo quiere ver nombres de definiciones de código fuente utilizados en este directorio:", "didViewDefinitions": "Roo vio nombres de definiciones de código fuente utilizados en este directorio:", "wantsToSearch": "<PERSON><PERSON> quiere buscar en este directorio <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> buscó en este directorio <code>{{regex}}</code>:"}, "commandOutput": "Salida del comando", "response": "Respuesta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "<PERSON>oo quiere usar una herramienta en el servidor MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON> quiere acceder a un recurso en el servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON>oo quiere cambiar a modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON>oo quiere cambiar a modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "<PERSON><PERSON> cambió a modo <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON> cambió a modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON>oo quiere crear una nueva subtarea en modo <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON> quiere finalizar esta subtarea", "newTaskContent": "Instrucciones de la subtarea", "completionContent": "Subtarea completada", "resultContent": "Resultados de la subtarea", "defaultResult": "Por favor, continúa con la siguiente tarea.", "completionInstructions": "¡Subtarea completada! Puedes revisar los resultados y sugerir correcciones o próximos pasos. Si todo se ve bien, confirma para devolver el resultado a la tarea principal."}, "questions": {"hasQuestion": "<PERSON>oo tiene una pregunta:"}, "taskCompleted": "<PERSON><PERSON> completada", "powershell": {"issues": "Parece que estás teniendo problemas con Windows PowerShell, por favor consulta esta"}, "autoApprove": {"title": "Auto-aprobar:", "none": "<PERSON><PERSON><PERSON>", "description": "Auto-aprobar permite a Roo Code realizar acciones sin pedir permiso. Habilita solo para acciones en las que confíes plenamente. Configuración más detallada disponible en <settingsLink>Configuración</settingsLink>."}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexto condensado", "condensing": "Condensando contexto...", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar a la entrada (o Shift + clic)"}, "announcement": {"title": "🎉 Roo Code {{version}} publicado", "description": "Roo Code {{version}} trae potentes nuevas funcionalidades y mejoras basadas en tus comentarios.", "whatsNew": "Novedades", "feature1": "<bold><PERSON><PERSON><PERSON>í<PERSON><PERSON> para <PERSON></bold>: Las llamadas a la API de Gemini ahora se almacenan automáticamente en caché, reduciendo los costos de API", "feature2": "<bold>Selección de modo más inteligente</bold>: Las definiciones de modo ahora pueden incluir orientación sobre cuándo debe usarse cada modo, permitiendo una mejor orquestación", "feature3": "<bold>Condensación inteligente de contexto</bold>: Resume de forma inteligente el historial de conversación cuando el contexto se llena en lugar de truncarlo (activar en Configuración -> Experimental)", "hideButton": "<PERSON><PERSON><PERSON><PERSON> anuncio", "detailsDiscussLinks": "Obtén más detalles y participa en <discordLink>Discord</discordLink> y <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "<PERSON>oo quiere usar el navegador:", "consoleLogs": "Registros de la consola", "noNewLogs": "(No hay nuevos registros)", "screenshot": "Captura de pantalla del navegador", "cursor": "cursor", "navigation": {"step": "Paso {{current}} de {{total}}", "previous": "Anterior", "next": "Siguient<PERSON>"}, "sessionStarted": "Sesión de navegador iniciada", "actions": {"title": "Acción de navegación: ", "launch": "Iniciar <PERSON> en {{url}}", "click": "Clic ({{coordinate}})", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> hacia abajo", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> hacia arriba", "close": "<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloque de código", "collapse": "Contraer bloque de código", "enable_wrap": "Activar ajuste de línea", "disable_wrap": "Desactivar ajuste de línea", "copy_code": "<PERSON><PERSON>r c<PERSON>"}}, "systemPromptWarning": "ADVERTENCIA: Anulación de instrucciones del sistema personalizada activa. Esto puede romper gravemente la funcionalidad y causar un comportamiento impredecible.", "shellIntegration": {"title": "Advertencia de ejecución de comandos", "description": "Tu comando se está ejecutando sin la integración de shell de terminal de VSCode. Para suprimir esta advertencia, puedes desactivar la integración de shell en la sección <strong>Terminal</strong> de la <settingsLink>configuración de Roo Code</settingsLink> o solucionar problemas de integración de terminal de VSCode usando el enlace de abajo.", "troubleshooting": "Haz clic aquí para ver la documentación de integración de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Límite de Solicitudes Auto-aprobadas Alcanzado", "description": "Roo ha alcanzado el límite auto-aprobado de {{count}} solicitud(es) API. ¿Deseas reiniciar el contador y continuar con la tarea?", "button": "Reiniciar y Continuar"}}}