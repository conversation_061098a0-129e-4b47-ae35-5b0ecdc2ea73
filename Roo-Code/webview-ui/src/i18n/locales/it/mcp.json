{"title": "Server MCP", "done": "<PERSON><PERSON>", "description": "Abilita il Model Context Protocol (MCP) per permettere a Roo Code di usare strumenti e servizi extra da server esterni. Questo amplia ciò che Roo può fare per te. <0>Scopri di più</0>", "enableToggle": {"title": "Abilita server MCP", "description": "Attiva questa opzione per permettere a Roo di usare strumenti dai server MCP collegati. Questo dà a Roo più capacità. Se non vuoi usare questi strumenti extra, disattiva per ridurre i costi dei token API."}, "enableServerCreation": {"title": "Abilita creazione server MCP", "description": "Abilita questa opzione per farti aiutare da Roo a creare <1>nuovi</1> server MCP personalizzati. <0>Scopri di più sulla creazione di server</0>", "hint": "Suggerimento: Per ridurre i costi dei token API, disattiva questa impostazione quando non chiedi a Roo di creare un nuovo server MCP."}, "editGlobalMCP": "Modifica MCP globale", "editProjectMCP": "Modifica MCP del progetto", "learnMoreEditingSettings": "Scopri di più sulla modifica dei file di configurazione MCP", "tool": {"alwaysAllow": "Consenti sempre", "parameters": "Parametri", "noDescription": "Nessuna descrizione"}, "tabs": {"tools": "Strumenti", "resources": "Risorse", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON><PERSON><PERSON> strumento trovato", "noResources": "Nessuna risorsa trovata", "noLogs": "Nessun log trovato", "noErrors": "<PERSON><PERSON><PERSON> errore trovato"}, "networkTimeout": {"label": "Timeout di rete", "description": "Tempo massimo di attesa per le risposte del server", "options": {"15seconds": "15 secondi", "30seconds": "30 secondi", "1minute": "1 minuto", "5minutes": "5 minuti", "10minutes": "10 minuti", "15minutes": "15 minuti", "30minutes": "30 minuti", "60minutes": "60 minuti"}}, "deleteDialog": {"title": "Elimina server MCP", "description": "Sei sicuro di voler eliminare il server MCP \"{{serverName}}\"? Questa azione non può essere annullata.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Elimina"}, "serverStatus": {"retrying": "Riprovo...", "retryConnection": "Riprova connessione"}}