{"greeting": "Benvenuto a Roo Code", "task": {"title": "Attività", "seeMore": "<PERSON><PERSON><PERSON>", "seeLess": "<PERSON><PERSON><PERSON> meno", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "Costo API:", "contextWindow": "Lunghezza del contesto:", "closeAndStart": "<PERSON>udi attività e iniziane una nuova", "export": "Esporta cronologia attività", "delete": "Elimina attività (Shift + Clic per saltare la conferma)", "condenseContext": "Condensa contesto attività"}, "unpin": "Rilascia", "pin": "<PERSON><PERSON>", "tokenProgress": {"availableSpace": "Spazio disponibile: {{amount}} tokens", "tokensUsed": "Tokens utilizzati: {{used}} di {{total}}", "reservedForResponse": "Riservato per risposta del modello: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Prova di nuovo l'operazione"}, "startNewTask": {"title": "Inizia nuova attività", "tooltip": "Inizia una nuova attività"}, "proceedAnyways": {"title": "Procedi comunque", "tooltip": "Continua mentre il comando è in esecuzione"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "Salva le modifiche al file"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Rifiuta questa azione"}, "completeSubtaskAndReturn": "Completa sottoattività e torna indietro", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Approva questa azione"}, "runCommand": {"title": "<PERSON><PERSON><PERSON><PERSON> comando", "tooltip": "Esegui questo comando"}, "proceedWhileRunning": {"title": "Procedi durante l'esecuzione", "tooltip": "Continua nonostante gli avvisi"}, "killCommand": {"title": "Termina comando", "tooltip": "Termina il comando corrente"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Continua l'attività corrente"}, "terminate": {"title": "Termina", "tooltip": "Termina l'attività corrente"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Annulla l'operazione corrente"}, "scrollToBottom": "<PERSON><PERSON><PERSON> fino alla fine della chat", "about": "<PERSON><PERSON>, refactor e debug del codice con l'assistenza dell'IA.<br />Consulta la nostra <DocsLink>documentazione</DocsLink> per saperne di più.", "onboarding": "Grazie alle più recenti innovazioni nelle capacità di codifica agentica, posso gestire complesse attività di sviluppo software passo dopo passo. Con strumenti che mi permettono di creare e modificare file, esplorare progetti complessi, utilizzare il browser ed eseguire comandi da terminale (dopo la tua autorizzazione), posso aiutarti in modi che vanno oltre il completamento del codice o il supporto tecnico. Posso persino usare MCP per creare nuovi strumenti ed estendere le mie capacità.", "rooTips": {"boomerangTasks": {"title": "Attività Boomerang", "description": "Dividi le attività in parti più piccole e gestibili."}, "stickyModels": {"title": "Modalità persistenti", "description": "Ogni modalità ricorda il tuo ultimo modello utilizzato"}, "tools": {"title": "Strumenti", "description": "Consenti all'IA di risolvere i problemi navigando sul Web, eseguendo comandi e altro ancora."}, "customizableModes": {"title": "Modalità personalizzabili", "description": "Personalità specializzate con comportamenti propri e modelli assegnati"}}, "selectMode": "Seleziona modalità di interazione", "selectApiConfig": "Seleziona la configurazione API", "enhancePrompt": "Migliora prompt con contesto aggiuntivo", "addImages": "Aggiungi immagini al messaggio", "sendMessage": "Invia messaggio", "typeMessage": "Scrivi un messaggio...", "typeTask": "Scrivi la tua attività qui...", "addContext": "@ per aggiungere contesto, / per cambiare modalità", "dragFiles": "tieni premuto shift per trascinare file", "dragFilesImages": "tieni premuto shift per trascinare file/immagini", "enhancePromptDescription": "Il pulsante 'Migliora prompt' aiuta a migliorare la tua richiesta fornendo contesto aggiuntivo, chiarimenti o riformulazioni. Prova a digitare una richiesta qui e fai di nuovo clic sul pulsante per vedere come funziona.", "errorReadingFile": "Errore nella lettura del file:", "noValidImages": "<PERSON>essuna immagine valida è stata elaborata", "separator": "Separatore", "edit": "Modifica...", "forNextMode": "per la prossima modalità", "instructions": {"wantsToFetch": "Roo vuole recuperare istruzioni dettagliate per aiutare con l'attività corrente"}, "error": "Errore", "diffError": {"title": "Modifica non riuscita"}, "troubleMessage": "Roo sta avendo problemi...", "apiRequest": {"title": "Richiesta API", "failed": "Richiesta API fallita", "streaming": "Richiesta API...", "cancelled": "Richiesta API annullata", "streamingFailed": "Streaming API fallito"}, "checkpoint": {"initial": "Checkpoint iniziale", "regular": "Checkpoint", "initializingWarning": "Inizializzazione del checkpoint in corso... Se questa operazione richiede troppo tempo, puoi disattivare i checkpoint nelle <settingsLink>impostazioni</settingsLink> e riavviare l'attività.", "menu": {"viewDiff": "Visualizza differenze", "restore": "Ripristina checkpoint", "restoreFiles": "R<PERSON><PERSON><PERSON> file", "restoreFilesDescription": "Rip<PERSON><PERSON> i file del tuo progetto a uno snapshot catturato in questo punto.", "restoreFilesAndTask": "Ripristina file e attività", "confirm": "Conferma", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Questa azione non può essere annullata.", "restoreFilesAndTaskDescription": "R<PERSON><PERSON><PERSON> i file del tuo progetto a uno snapshot catturato in questo punto ed elimina tutti i messaggi successivi a questo punto."}, "current": "<PERSON><PERSON><PERSON>"}, "fileOperations": {"wantsToRead": "Roo vuole leggere questo file:", "wantsToReadOutsideWorkspace": "Roo vuole leggere questo file al di fuori dell'area di lavoro:", "didRead": "Roo ha letto questo file:", "wantsToEdit": "Roo vuole modificare questo file:", "wantsToEditOutsideWorkspace": "Roo vuole modificare questo file al di fuori dell'area di lavoro:", "wantsToCreate": "Roo vuole creare un nuovo file:", "wantsToSearchReplace": "Roo vuole eseguire ricerca e sostituzione in questo file:", "didSearchReplace": "Roo ha eseguito ricerca e sostituzione in questo file:", "wantsToInsert": "Roo vuole inserire contenuto in questo file:", "wantsToInsertWithLineNumber": "Roo vuole inserire contenuto in questo file alla riga {{lineNumber}}:", "wantsToInsertAtEnd": "Roo vuole aggiungere contenuto alla fine di questo file:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo vuole visualizzare i file di primo livello in questa directory:", "didViewTopLevel": "Roo ha visualizzato i file di primo livello in questa directory:", "wantsToViewRecursive": "Roo vuole visualizzare ricorsivamente tutti i file in questa directory:", "didViewRecursive": "Roo ha visualizzato ricorsivamente tutti i file in questa directory:", "wantsToViewDefinitions": "Roo vuole visualizzare i nomi delle definizioni di codice sorgente utilizzate in questa directory:", "didViewDefinitions": "Roo ha visualizzato i nomi delle definizioni di codice sorgente utilizzate in questa directory:", "wantsToSearch": "Roo vuole cercare in questa directory <code>{{regex}}</code>:", "didSearch": "Roo ha cercato in questa directory <code>{{regex}}</code>:"}, "commandOutput": "Output del comando", "response": "Risposta", "arguments": "Argomenti", "mcp": {"wantsToUseTool": "Roo vuole utilizzare uno strumento sul server MCP {{serverName}}:", "wantsToAccessResource": "Roo vuole accedere a una risorsa sul server MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Roo vuole passare alla modalità <code>{{mode}}</code>", "wantsToSwitchWithReason": "Roo vuole passare alla modalità <code>{{mode}}</code> perché: {{reason}}", "didSwitch": "Roo è passato alla modalità <code>{{mode}}</code>", "didSwitchWithReason": "Roo è passato alla modalità <code>{{mode}}</code> perché: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo vuole creare una nuova sottoattività in modalità <code>{{mode}}</code>:", "wantsToFinish": "Roo vuole completare questa sottoattività", "newTaskContent": "Istruzioni sottoattività", "completionContent": "Sottoattività completata", "resultContent": "Risultati sottoattività", "defaultResult": "Per favore continua con la prossima attività.", "completionInstructions": "Sottoattività completata! Puoi rivedere i risultati e suggerire correzioni o prossimi passi. Se tutto sembra a posto, conferma per restituire il risultato all'attività principale."}, "questions": {"hasQuestion": "Roo ha una domanda:"}, "taskCompleted": "Attività completata", "powershell": {"issues": "<PERSON><PERSON>ra che tu stia avendo problemi con Windows PowerShell, consulta questa"}, "autoApprove": {"title": "Auto-approvazione:", "none": "Nessuna", "description": "L'auto-approvazione permette a Roo Code di eseguire azioni senza chiedere permesso. Abilita solo per azioni di cui ti fidi completamente. Configurazione più dettagliata disponibile nelle <settingsLink>Impostazioni</settingsLink>."}, "reasoning": {"thinking": "<PERSON><PERSON>", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contesto condensato", "condensing": "Condensazione del contesto...", "tokens": "token"}, "followUpSuggest": {"copyToInput": "Copia nell'input (o Shift + clic)"}, "announcement": {"title": "🎉 Rilasciato Roo Code {{version}}", "description": "Roo Code {{version}} introduce potenti nuove funzionalità e miglioramenti basati sui tuoi feedback.", "whatsNew": "Novità", "feature1": "<bold>Caching implicito per Gemini</bold>: Le chiamate API Gemini vengono ora memorizzate automaticamente nella cache, riducendo i costi API", "feature2": "<bold>Selezione della modalità più intelligente</bold>: Le definizioni delle modalità possono ora includere indicazioni su quando ogni modalità dovrebbe essere utilizzata, permettendo una migliore orchestrazione", "feature3": "<bold>Condensazione intelligente del contesto</bold>: Riassume in modo intelligente la cronologia delle conversazioni quando il contesto si riempie invece di troncarla (abilitare in Impostazioni -> Sperimentale)", "hideButton": "Nascondi annuncio", "detailsDiscussLinks": "Ottieni maggiori dettagli e partecipa alle discussioni su <discordLink>Discord</discordLink> e <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Roo vuole utilizzare il browser:", "consoleLogs": "Log della console", "noNewLogs": "(<PERSON><PERSON>un nuovo log)", "screenshot": "Screenshot del browser", "cursor": "cursore", "navigation": {"step": "Passo {{current}} di {{total}}", "previous": "Precedente", "next": "Successivo"}, "sessionStarted": "Sessione browser avviata", "actions": {"title": "Azione browser: ", "launch": "Avvia browser su {{url}}", "click": "Clic ({{coordinate}})", "type": "Digita \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON> verso il basso", "scrollUp": "<PERSON><PERSON><PERSON> verso l'alto", "close": "Chiudi browser"}}, "codeblock": {"tooltips": {"expand": "Espandi blocco di codice", "collapse": "Comprimi blocco di codice", "enable_wrap": "Attiva a capo automatico", "disable_wrap": "Disattiva a capo automatico", "copy_code": "Copia codice"}}, "systemPromptWarning": "ATTENZIONE: Sovrascrittura personalizzata delle istruzioni di sistema attiva. Questo può compromettere gravemente le funzionalità e causare comportamenti imprevedibili.", "shellIntegration": {"title": "Avviso di esecuzione comando", "description": "Il tuo comando viene eseguito senza l'integrazione shell del terminale VSCode. Per sopprimere questo avviso puoi disattivare l'integrazione shell nella sezione <strong>Terminal</strong> delle <settingsLink>impostazioni di Roo Code</settingsLink> o risolvere i problemi di integrazione del terminale VSCode utilizzando il link qui sotto.", "troubleshooting": "Clicca qui per la documentazione sull'integrazione shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limite di Richieste Auto-approvate Raggiunto", "description": "Roo ha raggiunto il limite auto-approvato di {{count}} richiesta/e API. Vuoi reimpostare il contatore e procedere con l'attività?", "button": "Reimposta e Continua"}}}