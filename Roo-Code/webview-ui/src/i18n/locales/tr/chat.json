{"greeting": "Roo Code'a Hoş Geldiniz", "task": {"title": "<PERSON><PERSON><PERSON><PERSON>", "seeMore": "<PERSON><PERSON> fazla gör", "seeLess": "<PERSON><PERSON> az gör", "tokens": "Tokenlar:", "cache": "Önbellek:", "apiCost": "API Maliyeti:", "contextWindow": "Bağlam Uzunluğu:", "closeAndStart": "<PERSON><PERSON><PERSON><PERSON> kapat ve yeni bir görev ba<PERSON><PERSON>", "export": "Görev geçmişini dışa aktar", "delete": "G<PERSON><PERSON>vi sil (Onayı atlamak için Shift + Tıkla)", "condenseContext": "G<PERSON>rev bağlamını yoğunlaştır"}, "unpin": "Sabitlemeyi iptal et", "pin": "<PERSON><PERSON><PERSON>", "tokenProgress": {"availableSpace": "Kullanılabil<PERSON> alan: {{amount}} token", "tokensUsed": "Kullanılan tokenlar: {{used}} / {{total}}", "reservedForResponse": "Model yanıtı için a<PERSON>ı<PERSON>: {{amount}} token"}, "retry": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "İşlemi tekrar dene"}, "startNewTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> bir g<PERSON><PERSON>v ba<PERSON><PERSON>"}, "proceedAnyways": {"title": "<PERSON><PERSON>", "tooltip": "Komut çalışırken devam et"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON>lerini kaydet"}, "reject": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "completeSubtaskAndReturn": "<PERSON> görevi tama<PERSON>la ve geri dön", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON> e<PERSON><PERSON> on<PERSON>la"}, "runCommand": {"title": "Komutu Çalıştır", "tooltip": "Bu komutu ç<PERSON>ıştır"}, "proceedWhileRunning": {"title": "Çalışırken Devam Et", "tooltip": "Uyarılara rağmen devam et"}, "killCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Mevcut komutu durdur"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Mevcut göreve devam et"}, "terminate": {"title": "Sonlandır", "tooltip": "Mevcut görevi <PERSON>"}, "cancel": {"title": "İptal", "tooltip": "Mevcut işlemi iptal et"}, "scrollToBottom": "<PERSON><PERSON><PERSON><PERSON> altına kaydır", "about": "AI yardımıyla kod <PERSON>, yeniden düzenleyin ve hatalarını ayıklayın.<br />Daha fazla bilgi edinmek için <DocsLink>belgelerimize</DocsLink> göz atın.", "onboarding": "<strong><PERSON>u çalışma alanındaki görev listeniz boş.</strong> Aşağıya bir görev yazarak başlayın. Nasıl başlayacağınızdan emin değil misiniz? Roo'nun sizin için neler yapabileceği hakkında daha fazla bilgiyi <DocsLink>belgelerde</DocsLink> okuyun.", "rooTips": {"boomerangTasks": {"title": "Bumerang Görevleri", "description": "Görevleri da<PERSON>, yönetilebilir par<PERSON>a a<PERSON>ırın."}, "stickyModels": {"title": "Yapışkan Modlar", "description": "Her mod, en son kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modeli hatırlar"}, "tools": {"title": "Araçlar", "description": "AI'nın web'e göz atarak, komutlar çalıştırarak ve daha fazlasını yaparak sorunları çözmesine izin verin."}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ları ve atanmış modelleri ile özelleştirilmiş kişilikler"}}, "selectMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modunu se<PERSON>", "selectApiConfig": "API yapılandırmasını seçin", "enhancePrompt": "Ek bağ<PERSON>la istemi gel<PERSON>ştir", "addImages": "<PERSON>ja resim ekle", "sendMessage": "<PERSON><PERSON>", "typeMessage": "Bir mesaj yazın...", "typeTask": "Görevinizi buraya yazın...", "addContext": "Bağlam eklemek için @, mod değiştirmek için /", "dragFiles": "dosyaları sürüklemek için shift tuşuna basılı tutun", "dragFilesImages": "dosyaları/resimleri sürüklemek için shift tuşuna basılı tutun", "enhancePromptDescription": "'İstemi geli<PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON>, ek b<PERSON><PERSON><PERSON>, açıklama veya yeniden ifade sağlayarak isteğinizi iyileştirmeye yardımcı olur. Buraya bir istek yazıp düğmeye tekrar tıklayarak nasıl çalıştığını görebilirsiniz.", "errorReadingFile": "<PERSON><PERSON>a okuma hatası:", "noValidImages": "Hiçbir geçerli resim işlenmedi", "separator": "Ayırıcı", "edit": "Düzenle...", "forNextMode": "<PERSON><PERSON><PERSON> mod i<PERSON>in", "error": "<PERSON><PERSON>", "diffError": {"title": "Düzenleme Başarısız"}, "troubleMessage": "<PERSON>oo sorun ya<PERSON>r...", "apiRequest": {"title": "API İsteği", "failed": "API İsteği Başarısız", "streaming": "API İsteği...", "cancelled": "API İsteği İptal Edildi", "streamingFailed": "API Akışı Başarısız"}, "checkpoint": {"initial": "İlk Kontrol Noktası", "regular": "<PERSON><PERSON><PERSON>", "initializingWarning": "Kontrol noktası hala başlatılıyor... Bu çok uzun sürerse, <settingsLink>ayarlar</settingsLink> bölümünden kontrol noktalarını devre dışı bırakabilir ve görevinizi yeniden başlatabilirsiniz.", "menu": {"viewDiff": "Farkları Görüntüle", "restore": "Kontrol Noktasını Geri <PERSON>", "restoreFiles": "Dosyaları Geri <PERSON>", "restoreFilesDescription": "Projenizin dosyalarını bu noktada alınan bir anlık görüntüye geri yükler.", "restoreFilesAndTask": "Dosyaları ve Görevi Geri <PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "cannotUndo": "Bu işlem geri alınamaz.", "restoreFilesAndTaskDescription": "Projenizin dosyalarını bu noktada alınan bir anlık görüntüye geri yükler ve bu noktadan sonraki tüm mesajları siler."}, "current": "Mevcut"}, "instructions": {"wantsToFetch": "Roo mevcut göreve yardımcı olmak için ayrıntılı talimatlar almak istiyor"}, "fileOperations": {"wantsToRead": "Roo bu dosyayı okumak istiyor:", "wantsToReadOutsideWorkspace": "R<PERSON> ç<PERSON>ışma alanı dışındaki bu dosyayı okumak istiyor:", "didRead": "Roo bu dosyayı okudu:", "wantsToEdit": "Roo bu dosyayı düzenlemek istiyor:", "wantsToEditOutsideWorkspace": "Roo çalışma alanı dışındaki bu dosyayı düzenlemek istiyor:", "wantsToCreate": "Roo yeni bir dosya oluşturmak istiyor:", "wantsToSearchReplace": "Roo bu dosyada arama ve değiştirme yapmak istiyor:", "didSearchReplace": "Roo bu dosyada arama ve değiştirme yaptı:", "wantsToInsert": "Roo bu dosyaya içerik eklemek istiyor:", "wantsToInsertWithLineNumber": "Roo bu dosyanın {{lineNumber}}. satırına içerik eklemek istiyor:", "wantsToInsertAtEnd": "Roo bu dosyanın sonuna içerik eklemek istiyor:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo bu dizindeki üst düzey dosyaları görüntülemek istiyor:", "didViewTopLevel": "Roo bu dizindeki üst düzey dosyaları görüntüledi:", "wantsToViewRecursive": "Roo bu dizindeki tüm dosyaları özyinelemeli olarak görüntülemek istiyor:", "didViewRecursive": "Roo bu dizindeki tüm dosyaları özyinelemeli olarak görüntüledi:", "wantsToViewDefinitions": "Roo bu dizinde kullanılan kaynak kod tanımlama isimlerini görüntülemek istiyor:", "didViewDefinitions": "Roo bu dizinde kullanılan kaynak kod tanımlama isimlerini görü<PERSON>üledi:", "wantsToSearch": "<PERSON>oo bu dizinde <code>{{regex}}</code> i<PERSON><PERSON> arama yapmak istiyor:", "didSearch": "<PERSON>oo bu dizinde <code>{{regex}}</code> i<PERSON><PERSON> arama yaptı:"}, "commandOutput": "Komut Çıktısı", "response": "Yan<PERSON>t", "arguments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mcp": {"wantsToUseTool": "Roo {{serverName}} MCP sunucusunda bir araç kullanmak istiyor:", "wantsToAccessResource": "Roo {{serverName}} MCP sunucusundaki bir kaynağa erişmek istiyor:"}, "modes": {"wantsToSwitch": "Roo <code>{{mode}}</code> moduna geçmek istiyor", "wantsToSwitchWithReason": "Roo <code>{{mode}}</code> moduna geçmek istiyor <PERSON>: {{reason}}", "didSwitch": "Roo <code>{{mode}}</code> moduna geçti", "didSwitchWithReason": "Roo <code>{{mode}}</code> moduna geç<PERSON>: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo <code>{{mode}}</code> modunda yeni bir alt görev oluşturmak istiyor:", "wantsToFinish": "Roo bu alt görevi bitirmek istiyor", "newTaskContent": "Alt Görev Talimatları", "completionContent": "Alt Görev Tamamlandı", "resultContent": "Alt Görev Sonuçları", "defaultResult": "Lütfen sonraki göreve devam edin.", "completionInstructions": "Alt görev tamamlandı! Sonuçları inceleyebilir ve düzeltmeler veya sonraki adımlar önerebilirsiniz. Her şey iyi görünü<PERSON><PERSON>, sonucu üst göreve döndürmek için on<PERSON>ın."}, "questions": {"hasQuestion": "R<PERSON>'nun bir sorusu var:"}, "taskCompleted": "Görev Tamamlandı", "powershell": {"issues": "Windows PowerShell ile ilgili sorunlar yaşıyor gibi görünüyorsunuz, lütfen şu konuya bakın"}, "autoApprove": {"title": "Otomatik-onay:", "none": "Hiç<PERSON>i", "description": "<PERSON><PERSON><PERSON>k onay, Roo Code'un izin istemeden işlemler gerçekleştirmesine olanak tanır. Yalnızca tamamen güvendiğiniz eylemler için etkinleştirin. Daha detaylı ya<PERSON>ılandırma <settingsLink>Ayarlar</settingsLink>'da mevcuttur."}, "reasoning": {"thinking": "Düşünüyor", "seconds": "{{count}}sn"}, "contextCondense": {"title": "Bağlam Özetlendi", "condensing": "Bağlam yoğunlaştırılıyor...", "tokens": "token"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON><PERSON> k<PERSON> (veya Shift + tıklama)"}, "announcement": {"title": "🎉 Roo Code {{version}} Yayınlandı", "description": "Roo Code {{version}} geri bildirimlerinize dayalı güçlü yeni özellikler ve iyileştirmeler getiriyor.", "whatsNew": "<PERSON><PERSON><PERSON><PERSON>", "feature1": "<bold>Gemini i<PERSON><PERSON> Önbelleğe Alma</bold>: Gemini API çağrıları artık otomatik olarak önbelleğe alınıyor, API maliyetlerini azaltıyor", "feature2": "<bold><PERSON><PERSON> Mod Seçimi</bold>: <PERSON><PERSON> artık her modun ne zaman kullanılması gerektiğine dair rehberlik içerebiliyor, daha iyi orkestrasyon sağlıyor", "feature3": "<bold>Akıllı Bağlam Yoğunlaştırma</bold>: Bağlam dolduğunda kesip atmak yerine konuşma geçmişini akıllıca özetliyor (Ayarlar -> Deneysel bölümünden etkinleştirin)", "hideButton": "<PERSON><PERSON><PERSON><PERSON> gizle", "detailsDiscussLinks": "<discordLink>Discord</discordLink> ve <redditLink>Reddit</redditLink> üzerinde daha fazla ayrıntı edinin ve tartışmalara katılın 🚀"}, "browser": {"rooWantsToUse": "<PERSON>oo tarayıcıyı kullanmak istiyor:", "consoleLogs": "Konsol Kayıtları", "noNewLogs": "(<PERSON><PERSON> kayıt yok)", "screenshot": "Tarayıcı ekran görüntüsü", "cursor": "imleç", "navigation": {"step": "Adım {{current}} / {{total}}", "previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>"}, "sessionStarted": "Tarayıcı Oturumu Başlatıldı", "actions": {"title": "Tarayıcı İşlemi: ", "launch": "{{url}} ad<PERSON><PERSON><PERSON> ba<PERSON>lat", "click": "<PERSON><PERSON><PERSON> ({{coordinate}})", "type": "Yaz \"{{text}}\"", "scrollDown": "Aşağı kaydır", "scrollUp": "Yukarı kaydır", "close": "Tarayıcıyı kapat"}}, "codeblock": {"tooltips": {"expand": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> g<PERSON>", "collapse": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "enable_wrap": "Sa<PERSON>ır kaydırmayı etkinleştir", "disable_wrap": "<PERSON><PERSON><PERSON>r kaydırmayı devre dışı bırak", "copy_code": "Kodu k<PERSON>ala"}}, "systemPromptWarning": "UYARI: <PERSON>zel sistem komut geçersiz kılma aktif. Bu işlevselliği ciddi şekilde bozabilir ve öngörülemeyen davranışlara neden olabilir.", "shellIntegration": {"title": "Komut Çalıştırma Uyarısı", "description": "Komutunuz VSCode terminal kabuk entegrasyonu olmadan çalıştırılıyor. Bu uyarıyı gizlemek için <settingsLink>Roo Code ayarları</settingsLink>'nın <strong>Terminal</strong> bölümünden kabuk entegrasyonunu devre dışı bırakabilir veya aşağıdaki bağlantıyı kullanarak VSCode terminal entegrasyonu sorunlarını giderebilirsiniz.", "troubleshooting": "Kabuk entegrasyonu belgelerini görmek için buraya tıklayın."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Otomatik Onaylanan İstek Limiti Aşıldı", "description": "<PERSON>oo, {{count}} API isteği/istekleri için otomatik onaylanan limite ulaştı. Sayacı sıfırlamak ve göreve devam etmek istiyor musunuz?", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve Devam Et"}}}