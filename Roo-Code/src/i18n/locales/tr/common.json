{"extension": {"name": "Roo Code", "description": "Düzenleyicinizde tam bir AI geliştirici ekibi."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON> geldiniz, {{name}}! {{count}} bild<PERSON><PERSON>z var.", "items": {"zero": "<PERSON><PERSON><PERSON> yok", "one": "B<PERSON>", "other": "{{count}} <PERSON><PERSON><PERSON>"}, "confirmation": {"reset_state": "Uzantıdaki tüm durumları ve gizli depolamayı sıfırlamak istediğinizden emin misiniz? Bu işlem geri alınamaz.", "delete_config_profile": "Bu yapılandırma profilini silmek istediğinizden emin misiniz?", "delete_custom_mode": "Bu özel modu silmek istediğinizden emin misiniz?", "delete_message": "<PERSON><PERSON><PERSON> si<PERSON> is<PERSON>?", "just_this_message": "<PERSON><PERSON><PERSON> bu mesajı", "this_and_subsequent": "Bu ve sonraki tüm mesajları"}, "errors": {"invalid_mcp_config": "Geçersiz proje MCP yapılandırma formatı", "invalid_mcp_settings_format": "Geçersiz MCP ayarları JSON formatı. Lütfen ayarlarınızın doğru JSON formatını takip ettiğinden emin olun.", "invalid_mcp_settings_syntax": "Geçersiz MCP ayarları JSON formatı. Lütfen ayarlar dosyanızda sözdizimi hatalarını kontrol edin.", "invalid_mcp_settings_validation": "Geçersiz MCP ayarları formatı: {{errorMessages}}", "failed_initialize_project_mcp": "Proje MCP sunucusu başlatılamadı: {{error}}", "invalid_data_uri": "Geçersiz veri URI formatı", "checkpoint_timeout": "Kontrol noktasını geri yüklemeye çalışırken zaman aşımına uğradı.", "checkpoint_failed": "Kontrol noktası geri <PERSON>i.", "no_workspace": "Lütfen önce bir proje klasörü açın", "update_support_prompt": "Destek istemi gü<PERSON>llenemedi", "reset_support_prompt": "Destek istemi sıfırl<PERSON>madı", "enhance_prompt": "İstem geliştirilemedi", "get_system_prompt": "Sistem istemi alı<PERSON>ı", "search_commits": "Taahhütler aranamadı", "save_api_config": "API yapılandırması kaydedilemedi", "create_api_config": "API yapılandırması oluşturulamadı", "rename_api_config": "API yapılandırmasının adı değiştirilemedi", "load_api_config": "API yapılandırması yüklenemedi", "delete_api_config": "API yapılandırması silinemedi", "list_api_config": "API yapılandırma listesi alınamadı", "update_server_timeout": "<PERSON><PERSON><PERSON> zaman aşımı güncellenemedi", "failed_update_project_mcp": "Proje MCP sunucuları güncellenemedi", "create_mcp_json": ".roo/mcp.json oluşturulamadı veya açılamadı: {{error}}", "hmr_not_running": "<PERSON><PERSON><PERSON> sun<PERSON>, HMR çalışmayacak. HMR'yi etkinleştirmek için uzantıyı başlatmadan önce lütfen 'npm run dev' komutunu çalıştırın.", "retrieve_current_mode": "Mevcut mod durumdan alı<PERSON>ı<PERSON>en hata oluştu.", "failed_delete_repo": "İlişkili gölge depo veya dal silinemedi: {{error}}", "failed_remove_directory": "G<PERSON><PERSON>v dizini kaldı<PERSON>ılamadı: {{error}}", "custom_storage_path_unusable": "<PERSON><PERSON> depolama yolu \"{{path}}\" k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, var<PERSON><PERSON><PERSON> yol kullanılacak", "cannot_access_path": "{{path}} yo<PERSON><PERSON>: {{error}}", "settings_import_failed": "Ayarlar içe aktarılamadı: {{error}}."}, "warnings": {"no_terminal_content": "Seçili terminal içeriği yok", "missing_task_files": "Bu görevin dosyaları eksik. Görev listesinden kaldırmak istiyor musunuz?"}, "info": {"no_changes": "Değişiklik bulunamadı.", "clipboard_copy": "Sistem istemi panoya başarıyla kopyalandı", "history_cleanup": "Geçmişten eksik dosyaları olan {{count}} görev temizlendi.", "mcp_server_restarting": "{{serverName}} MCP sunucusu yeniden başlatılıyor...", "mcp_server_connected": "{{serverName}} MCP sunucusu bağlandı", "mcp_server_deleted": "MCP sunucusu silindi: {{serverName}}", "mcp_server_not_found": "Ya<PERSON><PERSON>landırmada \"{{serverName}}\" sunucusu bulunamadı", "custom_storage_path_set": "<PERSON><PERSON> depolama yolu ayarlandı: {{path}}", "default_storage_path": "Varsayılan depolama yoluna geri <PERSON>", "settings_imported": "Ayarlar başarıyla içe aktarıldı."}, "answers": {"yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "cancel": "İptal", "remove": "Kaldır", "keep": "<PERSON><PERSON>"}, "tasks": {"canceled": "Görev hatası: Kullanıcı tarafından durduruldu ve iptal edildi.", "deleted": "Görev başarısız: Kullanıcı tarafından durduruldu ve silindi."}, "storage": {"prompt_custom_path": "Konuşma geçmişi için özel depolama yolunu girin, var<PERSON><PERSON><PERSON> konumu kullanmak için boş bırakın", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Lütfen mutlak bir yol girin (örn. D:\\RooCodeStorage veya /home/<USER>/storage)", "enter_valid_path": "Lütfen geçerli bir yol girin"}, "input": {"task_prompt": "Roo ne yapsın?", "task_placeholder": "<PERSON><PERSON><PERSON><PERSON>i buraya yaz"}, "settings": {"providers": {"groqApiKey": "Groq API Anahtarı", "getGroqApiKey": "Groq API Anahtarı Al"}}}