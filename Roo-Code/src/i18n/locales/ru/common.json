{"extension": {"name": "Roo Code", "description": "Целая команда ИИ-разработчиков в вашем редакторе."}, "number_format": {"thousand_suffix": "тыс", "million_suffix": "млн", "billion_suffix": "млрд"}, "welcome": "Добро пожаловать, {{name}}! У вас {{count}} уведомлений.", "items": {"zero": "Нет элементов", "one": "Один элемент", "other": "{{count}} элементов"}, "confirmation": {"reset_state": "Вы уверены, что хотите сбросить все состояние и секретное хранилище в расширении? Это действие нельзя отменить.", "delete_config_profile": "Вы уверены, что хотите удалить этот профиль конфигурации?", "delete_custom_mode": "Вы уверены, что хотите удалить этот пользовательский режим?", "delete_message": "Что вы хотите удалить?", "just_this_message": "Только это сообщение", "this_and_subsequent": "Это и все последующие сообщения"}, "errors": {"invalid_mcp_config": "Неверный формат конфигурации проекта MCP", "invalid_mcp_settings_format": "Неверный формат JSON настроек MCP. Пожалуйста, убедитесь, что ваши настройки соответствуют правильному формату JSON.", "invalid_mcp_settings_syntax": "Неверный формат JSON настроек MCP. Пожалуйста, проверьте ваш файл настроек на наличие синтаксических ошибок.", "invalid_mcp_settings_validation": "Неверный формат настроек MCP: {{errorMessages}}", "failed_initialize_project_mcp": "Не удалось инициализировать сервер проекта MCP: {{error}}", "invalid_data_uri": "Неверный формат URI данных", "checkpoint_timeout": "Превышено время ожидания при попытке восстановления контрольной точки.", "checkpoint_failed": "Не удалось восстановить контрольную точку.", "no_workspace": "Пожалуйста, сначала откройте папку проекта", "update_support_prompt": "Не удалось обновить промпт поддержки", "reset_support_prompt": "Не удалось сбросить промпт поддержки", "enhance_prompt": "Не удалось улучшить промпт", "get_system_prompt": "Не удалось получить системный промпт", "search_commits": "Не удалось выполнить поиск коммитов", "save_api_config": "Не удалось сохранить конфигурацию API", "create_api_config": "Не удалось создать конфигурацию API", "rename_api_config": "Не удалось переименовать конфигурацию API", "load_api_config": "Не удалось загрузить конфигурацию API", "delete_api_config": "Не удалось удалить конфигурацию API", "list_api_config": "Не удалось получить список конфигураций API", "update_server_timeout": "Не удалось обновить таймаут сервера", "create_mcp_json": "Не удалось создать или открыть .roo/mcp.json: {{error}}", "hmr_not_running": "Локальный сервер разработки не запущен, HMR не будет работать. Пожалуйста, запустите 'npm run dev' перед запуском расширения для включения HMR.", "retrieve_current_mode": "Ошибка: не удалось получить текущий режим из состояния.", "failed_delete_repo": "Не удалось удалить связанный теневой репозиторий или ветку: {{error}}", "failed_remove_directory": "Не удалось удалить директорию задачи: {{error}}", "custom_storage_path_unusable": "Пользовательский путь хранения \"{{path}}\" непригоден, будет использован путь по умолчанию", "cannot_access_path": "Невозможно получить доступ к пути {{path}}: {{error}}", "failed_update_project_mcp": "Не удалось обновить серверы проекта MCP", "settings_import_failed": "Не удалось импортировать настройки: {{error}}."}, "warnings": {"no_terminal_content": "Не выбрано содержимое терминала", "missing_task_files": "Файлы этой задачи отсутствуют. Хотите удалить её из списка задач?"}, "info": {"no_changes": "Изменения не найдены.", "clipboard_copy": "Системный промпт успешно скопирован в буфер обмена", "history_cleanup": "Очищено {{count}} зада<PERSON>(и) с отсутствующими файлами из истории.", "mcp_server_restarting": "Перезапуск сервера MCP {{serverName}}...", "mcp_server_connected": "Сервер MCP {{serverName}} подключен", "mcp_server_deleted": "Удален сервер MCP: {{serverName}}", "mcp_server_not_found": "Сервер \"{{serverName}}\" не найден в конфигурации", "custom_storage_path_set": "Установлен пользовательский путь хранения: {{path}}", "default_storage_path": "Возвращено использование пути хранения по умолчанию", "settings_imported": "Настройки успешно импортированы."}, "answers": {"yes": "Да", "no": "Нет", "cancel": "Отмена", "remove": "Удалить", "keep": "Оставить"}, "tasks": {"canceled": "Ошибка задачи: Она была остановлена и отменена пользователем.", "deleted": "Сбой задачи: Она была остановлена и удалена пользователем."}, "storage": {"prompt_custom_path": "Введите пользовательский путь хранения истории разговоров, оставьте пустым для использования расположения по умолчанию", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Пожалуйста, введите абсолютный путь (например, D:\\RooCodeStorage или /home/<USER>/storage)", "enter_valid_path": "Пожалуйста, введите корректный путь"}, "input": {"task_prompt": "Что должен сделать Roo?", "task_placeholder": "Введите вашу задачу здесь"}, "settings": {"providers": {"groqApiKey": "Ключ API Groq", "getGroqApiKey": "Получить ключ API Groq"}}}