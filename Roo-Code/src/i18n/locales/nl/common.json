{"extension": {"name": "Roo Code", "description": "<PERSON><PERSON> compleet ontwi<PERSON><PERSON><PERSON><PERSON> van AI-agenten in je editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "mrd"}, "welcome": "<PERSON><PERSON><PERSON>, {{name}}! Je hebt {{count}} meldingen.", "items": {"zero": "Geen items", "one": "Eén item", "other": "{{count}} items"}, "confirmation": {"reset_state": "Weet je zeker dat je alle status en geheime opslag in de extensie wilt resetten? Dit kan niet ongedaan worden gemaakt.", "delete_config_profile": "Weet je zeker dat je dit configuratieprofiel wilt verwijderen?", "delete_custom_mode": "Weet je zeker dat je deze aangepaste modus wilt verwijderen?", "delete_message": "Wat wil je verwijderen?", "just_this_message": "Alleen dit bericht", "this_and_subsequent": "Dit en alle volgende berichten"}, "errors": {"invalid_mcp_config": "Ongeldig project MCP-configuratieformaat", "invalid_mcp_settings_format": "Ongeldig MCP-instellingen JSON-formaat. Zorg ervoor dat je instellingen het juiste JSON-formaat volgen.", "invalid_mcp_settings_syntax": "Ongeldig MCP-instellingen JSON-formaat. Controleer je instellingenbestand op syntaxfouten.", "invalid_mcp_settings_validation": "Ongeldig MCP-instellingenformaat: {{errorMessages}}", "failed_initialize_project_mcp": "Initialiseren van project MCP-server mislukt: {{error}}", "invalid_data_uri": "Ongeldig data-URI-formaat", "checkpoint_timeout": "Time-out bij het hers<PERSON><PERSON> van checkpoint.", "checkpoint_failed": "Herstellen van checkpoint mislukt.", "no_workspace": "Open eerst een projectmap", "update_support_prompt": "Bijwerken van ondersteuningsprompt mislukt", "reset_support_prompt": "<PERSON><PERSON><PERSON> van ondersteuningsprompt mislukt", "enhance_prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> van prompt mislukt", "get_system_prompt": "<PERSON><PERSON><PERSON> van <PERSON> mislukt", "search_commits": "<PERSON><PERSON> naar commits mislukt", "save_api_config": "<PERSON><PERSON><PERSON> van API-configuratie mis<PERSON>t", "create_api_config": "Aanmaken van API-configuratie mis<PERSON>t", "rename_api_config": "<PERSON><PERSON><PERSON><PERSON> van <PERSON>-configuratie mis<PERSON>t", "load_api_config": "<PERSON><PERSON> van <PERSON>-configuratie mis<PERSON>t", "delete_api_config": "Verwijderen van API-configuratie mislukt", "list_api_config": "<PERSON><PERSON><PERSON> met API-configuraties mislukt", "update_server_timeout": "Bijwerken van server-timeout mislukt", "create_mcp_json": "Aanmaken of openen van .roo/mcp.json mislukt: {{error}}", "hmr_not_running": "Lokale ontwikkelserver d<PERSON>ait niet, HMR werkt niet. Voer 'npm run dev' uit voordat je de extensie start om HMR in te schakelen.", "retrieve_current_mode": "Fout: <PERSON><PERSON><PERSON> van h<PERSON> modus uit status mislukt.", "failed_delete_repo": "Verwijderen van gekoppelde schaduwrepository of branch mislukt: {{error}}", "failed_remove_directory": "Verwij<PERSON><PERSON> van taakmap mislukt: {{error}}", "custom_storage_path_unusable": "Aangepast opslagpad \"{{path}}\" is onb<PERSON><PERSON><PERSON>ar, standaardpad wordt gebruikt", "cannot_access_path": "Kan pad {{path}} niet openen: {{error}}", "failed_update_project_mcp": "Bijwerken van project MCP-servers mislukt", "settings_import_failed": "Importeren van instellingen mislukt: {{error}}."}, "warnings": {"no_terminal_content": "<PERSON><PERSON> <PERSON><PERSON> gese<PERSON>d", "missing_task_files": "De bestanden van deze taak ontbreken. Wil je deze uit de takenlijst verwijderen?"}, "info": {"no_changes": "Geen wijzigingen gevonden.", "clipboard_copy": "Systeemprompt succesvol gekopieerd naar klembord", "history_cleanup": "{{count}} taak/taken met ontbrekende bestanden uit geschiedenis verwijderd.", "mcp_server_restarting": "{{serverName}} MCP-server wordt opnieuw gestart...", "mcp_server_connected": "{{serverName}} MCP-server verbonden", "mcp_server_deleted": "MCP-server verwijderd: {{serverName}}", "mcp_server_not_found": "Server \"{{server<PERSON><PERSON>}}\" niet gevonden in configuratie", "custom_storage_path_set": "Aangepast opslagpad ingesteld: {{path}}", "default_storage_path": "Terug naar standaard opslagpad", "settings_imported": "Instellingen succesvol geïmporteerd."}, "answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Verwijderen", "keep": "Behouden"}, "tasks": {"canceled": "Taakfout: gestopt en geannuleerd door gebruiker.", "deleted": "Taakfout: gestopt en verwijderd door gebruiker."}, "storage": {"prompt_custom_path": "Voer een aangepast opslagpad voor gespreksgeschiedenis in, laat leeg voor standaardlocatie", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Voer een absoluut pad in (bijv. D:\\RooCodeStorage of /home/<USER>/storage)", "enter_valid_path": "<PERSON>oer een geldig pad in"}, "input": {"task_prompt": "Wat moet Roo doen?", "task_placeholder": "<PERSON>p hier je taak"}}