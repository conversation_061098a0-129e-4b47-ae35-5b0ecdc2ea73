name: Build VSIX

on:
  pull_request:
    types: [labeled]
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.event.label.name == 'build'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version-file: 'package.json'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install all dependencies
      run: npm run install:all

    - name: Build Extension
      run: npm run build

    - name: Upload VSIX artifact
      uses: actions/upload-artifact@v4
      with:
        name: extension-vsix
        path: bin/*.vsix

    - name: Comment PR with artifact link
      if: github.event_name == 'pull_request'
      uses: peter-evans/create-or-update-comment@v4
      with:
        issue-number: ${{ github.event.pull_request.number }}
        body: |
          Build successful! 🚀
          You can download the VSIX extension [here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}).
