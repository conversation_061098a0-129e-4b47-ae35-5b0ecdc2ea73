# Default
.changeset/**
.github/**
.husky/**
.vscode/**
coverage/**
node_modules/**
src/**
scripts/**
.gitignore
esbuild.js
jest.*
**/tsconfig.json
**/.eslintrc.json
.prettierignore
**/*.map
**/*.ts
**/.gitignore

# Custom
.env.sample
.git-blame-ignore-revs
.gitconfig
.gitattributes
.tool-versions
.vite-port
.nvmrc
.clinerules*
.roomodes
.rooignore
.roo/**
benchmark/**
docs/**
e2e/**
evals/**
locales/**
out/**
ellipsis.yaml
knip.json

# Ignore all webview-ui files except the build directory.
# https://github.com/microsoft/vscode-webview-ui-toolkit-samples/blob/main/frameworks/hello-world-react-cra/.vscodeignore
webview-ui/src/**
webview-ui/public/**
webview-ui/scripts/**
webview-ui/index.html
webview-ui/README.md
webview-ui/package.json
webview-ui/package-lock.json
webview-ui/node_modules/**

# Include codicons
!node_modules/@vscode/codicons/dist/codicon.css
!node_modules/@vscode/codicons/dist/codicon.ttf

# Include material icons
!node_modules/vscode-material-icons/generated/**

# Include default themes JSON files used in getTheme
!src/integrations/theme/default-themes/**

# Ignore doc assets
assets/docs/**

# Include icons and images
!assets/icons/**
!assets/images/**

# Include .env file for telemetry
!.env

# Ignore IntelliJ and Qodo plugin folders
.idea/**
.qodo/**