{"name": "e2e", "version": "0.1.0", "private": true, "scripts": {"lint": "eslint src/**/*.ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "npm run build && npx dotenvx run -f .env.local -- node ./out/runTest.js", "ci": "npm run vscode-test && npm run test", "build": "rimraf out && tsc -p tsconfig.json", "vscode-test": "cd .. && npm run vscode-test"}, "devDependencies": {"@roo-code/types": "^1.12.0", "@types/mocha": "^10.0.10", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.0", "mocha": "^11.1.0", "typescript": "5.8.3"}}