# Simple workflow for deploying the Mode Configurator static content to GitHub Pages
name: Deploy Mode Configurator to GitHub Pages

on:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"] # Or your default branch name
    paths:
      - 'tools/mode_configurator/**' # Only run if files in this directory change
      - '.github/workflows/deploy-configurator.yml' # Or if the workflow itself changes

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Build job - Now just prepares the artifact
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          # Upload the mode_configurator directory directly
          path: './tools/mode_configurator'

  # Deployment job
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4