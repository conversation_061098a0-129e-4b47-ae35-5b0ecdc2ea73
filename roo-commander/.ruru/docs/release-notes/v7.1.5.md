# Release Notes - v7.1.5

This release includes several new developer modes, documentation updates, and internal improvements.

## ✨ Features

*   `57192f6` - Update project configuration and documentation (Jez)
*   `03cacc8` - Update workflow definitions (Jez)
*   `f8ac861` - Added Rust Developer (dev-rust) mode. (#33) (<PERSON>)
*   `b74b903` - Java and Kotlin Developer Modes (#32) (<PERSON>)
*   `5983c4d` - new custom mode dev-solidity (#29) (beruf)
*   `f186b69` - Added Spring framework mode. (#31) (<PERSON>)

## 🐛 Fixes

*   `6894ded` - fixing and improving prompts (Jez)

## 📄 Docs

*   `5a29fbc` - Update guides (Jez)
*   `8041913` - Update README with setup info and improvements (Jez)

## ♻️ Refactor

*   `246f9d1` - Rename Repomix mode folder to lowercase and update references (Jez)

## 🧹 Chores

*   `e45679e` - update generated mode files after build (Jez)