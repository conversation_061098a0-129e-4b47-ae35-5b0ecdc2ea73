+++
# --- Basic Metadata (Template Definition) ---
# template_id = "TOML-MD-TEMPLATE-RELEASE-NOTES-V1"
# template_name = "Standard Release Notes/Changelog File"
# template_description = "A template for generating local release notes files, summarizing changes between Git tags."
# template_version = "1.0"
# template_status = "active"
# --- TOML Schema for Release Notes Files ---
id = "RELEASE-NOTES-v7.1.4"
title = "Release Notes - v7.1.4"
version = "v7.1.4"
release_date = "2025-04-29" # Using today's date
status = "draft"
tags = ["release-notes", "changelog", "v7.1.4"]
related_tags = ["v7.1.3", "v7.1.4"]
summary = "Minor documentation update." # Added a brief summary
# --- Related Context ---
# related_context = []
+++

# Release Notes - v7.1.4

> **Release Date:** 2025-04-29
> **Generated from:** `v7.1.3`...`v7.1.4`

Minor documentation update regarding the IntelliManage project planning system.

## ✨ New Features

*(No features in this release)*

## 🐛 Bug Fixes

*(No fixes in this release)*

## ⚡ Performance Improvements

*(No performance improvements in this release)*

## ♻️ Refactors

*(No refactors in this release)*

## 🧹 Chores / Internal

*(No chores in this release)*

## 📝 Documentation

- More details about project planning system intellimanage (`75d7c89`)

## 🧪 Tests

*(No tests added/changed in this release)*

*(Add other sections like 'Breaking Changes' (`BREAKING CHANGE:` footer) if needed based on Conventional Commit parsing)*