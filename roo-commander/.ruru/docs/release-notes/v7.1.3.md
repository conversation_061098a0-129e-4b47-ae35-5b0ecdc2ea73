+++
# --- Basic Metadata (Template Definition) ---
# template_id = "TOML-MD-TEMPLATE-RELEASE-NOTES-V1"
# template_name = "Standard Release Notes/Changelog File"
# template_description = "A template for generating local release notes files, summarizing changes between Git tags."
# template_version = "1.0"
# template_status = "active"
# --- TOML Schema for Release Notes Files ---
id = "RELEASE-NOTES-v7.1.3"
title = "Release Notes - v7.1.3"
version = "v7.1.3"
release_date = "2025-04-28"
status = "draft"
tags = ["release-notes", "changelog", "v7.1.3"]
related_tags = ["v7.1.2", "v7.1.3"]
summary = ""
# --- Related Context ---
# related_context = []
+++

# Release Notes - v7.1.3

> **Release Date:** 2025-04-28
> **Generated from:** `v7.1.2`...`v7.1.3`

*(Optional: Add a brief overall summary of the release here.)*

## ✨ New Features

- build: Add collection build script and update main workflow (`5770fe7`)
- build: Integrate mode collection builds into main workflow (`956e074`)

## 🐛 Bug Fixes

*(No fixes in this release)*

## ⚡ Performance Improvements

*(No performance improvements in this release)*

## ♻️ Refactors

- Reorganize build scripts and workflows, update docs (`135839c`)
- build: Split build process into workspace and distribution workflows (`8e7b6bd`)

## 🧹 Chores / Internal

- project: Refactor documentation structure and update build scripts (`940474f`)

## 📝 Documentation

*(No documentation changes in this release)*

## 🧪 Tests

*(No tests added/changed in this release)*

*(Add other sections like 'Breaking Changes' (`BREAKING CHANGE:` footer) if needed based on Conventional Commit parsing)*