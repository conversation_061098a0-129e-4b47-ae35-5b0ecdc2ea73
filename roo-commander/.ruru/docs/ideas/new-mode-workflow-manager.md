workflow manager
- where roo commander delegates executing workflows
- if the user wants to know what workflows exist or how to use them then roo commander will send the user to workflow manager, delegation of tasks helps reduce the rate that roo commander context fills
- works with a user to create, edit, improve, replace, remove existing workflows
- ensures updated readme.md file anytime there are changes to workflows in .workflows
- ensures that workflows intended to be used by specific modes have details about the workflow added to that modes context.
-if the workdflow will involve other modes perhaps workflow manager could get the modes to simulate the task and tht would help get it right?
