+++
id = "STD-MODE-SELECTION-GUIDE-V1"
title = "Mode Selection & Discovery Guide"
context_type = "standard"
scope = "Provides guidance for selecting the appropriate mode for task delegation"
target_audience = ["roo-commander", "prime-coordinator", "lead-*", "manager-*", "all"] # Anyone delegating tasks
granularity = "detailed"
status = "active"
last_updated = "2025-04-25" # Use current date
tags = ["modes", "delegation", "selection", "discovery", "standard", "documentation", "hierarchy", "collaboration"]
related_context = [
    ".ruru/docs/standards/mode_naming_standard.md",
    ".ruru/modes/roo-commander/kb/available-modes-summary.md",
    ".roo/rules-roo-commander/03-delegation-simplified.md"
    ]
template_schema_doc = ".ruru/templates/toml-md/16_ai_rule.README.md"
relevance = "Critical: Essential for effective task delegation and coordination"
+++

# Mode Selection & Discovery Guide

## 1. Purpose

This guide provides structured information and principles to assist all modes (especially coordinators like `roo-commander`, Leads, and Managers) in selecting the most appropriate specialist mode for a given task. Effective delegation is key to efficient project execution.

## 2. General Selection Principles

1.  **Specificity First:** Always prefer a specialist mode whose core purpose directly matches the task over a generalist mode (e.g., use `framework-react` for React work over `util-senior-dev`).
2.  **Match Keywords & Tags:** Use the task description's keywords and the project's `stack_profile.json` to find modes with matching `tags` or capabilities.
3.  **Consider Hierarchy:** Understand the typical flow: Managers/Commander delegate to Leads or Agents; Leads delegate to Specialists or Agents within their domain. Use `core-architect` for high-level design, Leads for domain coordination, Specialists for implementation.
4.  **Consult Stack Profile:** Check `.ruru/context/stack_profile.json` for project-specific technology choices that might favour certain framework or data specialists.
5.  **Review Capabilities:** If unsure between similar modes, review their specific `Key Capabilities` listed below.
6.  **Use MDTM Appropriately:** Delegate complex, stateful, or high-risk tasks using the MDTM workflow (Rule `04-mdtm-workflow-initiation.md`). Use simple `new_task` for straightforward requests.
7.  **When in Doubt, Ask:** If unsure after consulting this guide, use `ask_followup_question` to confirm the best mode with the user or a higher-level coordinator.

## 3. Mode Details

*(Note: The detailed information below, especially under "Hierarchy & Collaboration", is automatically generated by `build_mode_selection_guide_data.js` parsing individual `.mode.md` files. Manual updates should be avoided.)*

---

### `agent-context-condenser` (❓ 🗜️ Context Condenser)

*   **Core Purpose:** Generates dense, structured summaries (Condensed Context Indices) from technical documentation sources for embedding into other modes' instructions
*   **Key Capabilities:** Writing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `lead-*`, `manager-project`
    *   **Typical Reports To:** `roo-commander`, `relevant lead-*`
    *   **Frequent Collaborators:** `other agent-*`, `spec-*`, `lead-*`

---

---

### `agent-context-discovery` (❓ 🕵️ Discovery Agent)

*   **Core Purpose:** Specialized assistant for exploring the project workspace, analyzing files, and retrieving context
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `lead-*`, `manager-project`
    *   **Typical Reports To:** `roo-commander`, `relevant lead-*`
    *   **Frequent Collaborators:** `other agent-*`, `spec-*`, `lead-*`

---

---

### `agent-context-resolver` (❓ 📖 Context Resolver)

*   **Core Purpose:** Specialist in reading project documentation (task logs, decision records, planning files) to provide concise, accurate summaries of the current project state. Acts as the primary information retrieval and synthesis service for other modes
*   **Key Capabilities:** Writing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `lead-*`, `manager-project`
    *   **Typical Reports To:** `roo-commander`, `relevant lead-*`
    *   **Frequent Collaborators:** `other agent-*`, `spec-*`, `lead-*`

---

---

### `agent-file-repair` (❓ 🩹 File Repair Specialist)

*   **Core Purpose:** Attempts to fix corrupted or malformed text files (such as source code, JSON, YAML, configs) by addressing common issues like encoding errors, basic syntax problems, truncation, and invalid characters
*   **Key Capabilities:** Debugging
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `lead-*`, `manager-project`
    *   **Typical Reports To:** `roo-commander`, `relevant lead-*`
    *   **Frequent Collaborators:** `other agent-*`, `spec-*`, `lead-*`

---

---

### `agent-mcp-manager` (❓ 🛠️ MCP Manager Agent)

*   **Core Purpose:** Guides the user through installing, configuring, and potentially managing MCP servers interactively
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `lead-*`, `manager-project`
    *   **Typical Reports To:** `roo-commander`, `relevant lead-*`
    *   **Frequent Collaborators:** `other agent-*`, `spec-*`, `lead-*`

---

---

### `agent-research` (❓ 🌐 Research & Context Builder)

*   **Core Purpose:** Researches topics using web sources, code repositories, and local files, evaluates sources, gathers data, and synthesizes findings into structured summaries with citations
*   **Key Capabilities:** Analysis
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `lead-*`, `manager-project`
    *   **Typical Reports To:** `roo-commander`, `relevant lead-*`
    *   **Frequent Collaborators:** `other agent-*`, `spec-*`, `lead-*`

---

---

### `agent-session-summarizer` (❓ ⏱️ Session Summarizer)

*   **Core Purpose:** Reads project state artifacts (task logs, plans) to generate a concise handover summary
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `lead-*`, `manager-project`
    *   **Typical Reports To:** `roo-commander`, `relevant lead-*`
    *   **Frequent Collaborators:** `other agent-*`, `spec-*`, `lead-*`

---

---

### `auth-clerk` (❓ 🔑 Clerk Auth Specialist)

*   **Core Purpose:** Specializes in implementing secure authentication and user management using Clerk, covering frontend/backend integration, route protection, session handling, and advanced features
*   **Key Capabilities:** Implementation, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-security`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-security`, `manager-project`
    *   **Frequent Collaborators:** `lead-security`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `auth-firebase` (🧯 🧯 Firebase Auth Specialist)

*   **Core Purpose:** Implements and manages user authentication and authorization using Firebase Authentication, including Security Rules and frontend integration. Specializes in configuring Firebase Auth providers, implementing authentication flows, managing user sessions, and defining access control rules within the Firebase ecosystem
*   **Key Capabilities:** Implementation, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-security`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-security`, `manager-project`
    *   **Frequent Collaborators:** `lead-security`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `auth-supabase` (❓ 🔐 Supabase Auth Specialist)

*   **Core Purpose:** Implements and manages user authentication and authorization using Supabase Auth, including RLS policies and frontend integration
*   **Key Capabilities:** Implementation, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-security`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-security`, `manager-project`
    *   **Frequent Collaborators:** `lead-security`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `baas-firebase` (❓ 🔥 Firebase Developer)

*   **Core Purpose:** Expert in designing, building, and managing applications using the comprehensive Firebase platform
*   **Key Capabilities:** Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `baas-supabase` (❓ 🦸 Supabase Developer)

*   **Core Purpose:** Expert in leveraging the full Supabase suite (Postgres, Auth, Storage, Edge Functions, Realtime) using best practices
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `cloud-aws` (❓ ☁️ AWS Architect)

*   **Core Purpose:** Designs, implements, and manages secure, scalable, and cost-effective AWS infrastructure solutions. Translates requirements into cloud architecture and IaC
*   **Key Capabilities:** Implementation, Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `lead-*`, `manager-project`, `other architects`

---

---

### `cloud-azure` (❓ 🌐 Azure Architect)

*   **Core Purpose:** Specialized Lead for designing, implementing, managing, and optimizing Azure infrastructure solutions using IaC
*   **Key Capabilities:** Implementation, Design, Infrastructure
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `lead-*`, `manager-project`, `other architects`

---

---

### `cloud-gcp` (🌎 🌎 GCP Architect)

*   **Core Purpose:** A specialized lead-level mode responsible for designing, implementing, and managing secure, scalable, and cost-effective Google Cloud Platform (GCP) infrastructure solutions. Translates high-level requirements into concrete cloud architecture designs and Infrastructure as Code (IaC) implementations
*   **Key Capabilities:** Implementation, Design, Infrastructure
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `lead-*`, `manager-project`, `other architects`

---

---

### `cms-directus` (❓ 🎯 Directus Specialist)

*   **Core Purpose:** You are Roo Directus Specialist, responsible for implementing sophisticated solutions using the Directus headless CMS (typically v9+)
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `cms-wordpress` (❓ 🇼 WordPress Specialist)

*   **Core Purpose:** Responsible for implementing and customizing WordPress solutions
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `core-architect` (❓ 🏗️ Technical Architect)

*   **Core Purpose:** Designs and oversees high-level system architecture, making strategic technology decisions that align with business goals and technical requirements. Responsible for establishing the technical vision, selecting appropriate technologies, evaluating architectural trade-offs, addressing non-functional requirements, and ensuring technical coherence across the project. Acts as the primary technical decision-maker and advisor for complex system design challenges
*   **Key Capabilities:** Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `lead-*`, `manager-project`, `other architects`

---

---

### `data-dbt` (❓ 🔄 dbt Specialist)

*   **Core Purpose:** A specialized data transformation mode focused on implementing and managing dbt projects. Expert in creating efficient data models, configuring transformations, and implementing testing strategies. Specializes in creating maintainable, well-documented data transformations that follow best practices for modern data warehouses
*   **Key Capabilities:** Implementation, Testing, Writing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-db`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-db`, `manager-project`
    *   **Frequent Collaborators:** `lead-db`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `data-elasticsearch` (❓ 🔍 Elasticsearch Specialist)

*   **Core Purpose:** Designs, implements, queries, manages, and optimizes Elasticsearch clusters for search, logging, analytics, and vector search applications
*   **Key Capabilities:** Implementation, Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-db`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-db`, `manager-project`
    *   **Frequent Collaborators:** `lead-db`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `data-mongo` (❓ 🍃 MongoDB Specialist)

*   **Core Purpose:** Designs, implements, manages, and optimizes MongoDB databases, focusing on schema design, indexing, aggregation pipelines, and performance tuning
*   **Key Capabilities:** Implementation, Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-db`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-db`, `manager-project`
    *   **Frequent Collaborators:** `lead-db`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `data-mysql` (❓ 🐬 MySQL Specialist)

*   **Core Purpose:** Designs, implements, manages, and optimizes relational databases using MySQL, focusing on schema design, SQL queries, indexing, and performance
*   **Key Capabilities:** Implementation, Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-db`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-db`, `manager-project`
    *   **Frequent Collaborators:** `lead-db`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `data-neon` (❓ 🐘 Neon DB Specialist)

*   **Core Purpose:** Designs, implements, and manages Neon serverless PostgreSQL databases, including branching, connection pooling, and optimization
*   **Key Capabilities:** Implementation, Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-db`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-db`, `manager-project`
    *   **Frequent Collaborators:** `lead-db`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `data-specialist` (❓ 💾 Database Specialist)

*   **Core Purpose:** Designs, implements, optimizes, and maintains SQL/NoSQL databases, focusing on schema design, ORMs, migrations, query optimization, data integrity, and performance
*   **Key Capabilities:** Implementation, Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-db`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-db`, `manager-project`
    *   **Frequent Collaborators:** `lead-db`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-animejs` (❓ ✨ anime.js Specialist)

*   **Core Purpose:** Expert in creating complex, performant web animations using anime.js, including timelines, SVG morphing, interactive, and scroll-triggered effects
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-antd` (❓ 🐜 Ant Design Specialist)

*   **Core Purpose:** Implements and customizes React components using Ant Design, focusing on responsiveness, accessibility, performance, and best practices
*   **Key Capabilities:** Implementation, Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-bootstrap` (❓ 🅱️ Bootstrap Specialist)

*   **Core Purpose:** Specializes in building responsive websites and applications using the Bootstrap framework (v4 & v5), focusing on grid mastery, component usage, utilities, customization, and accessibility
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-d3` (❓ 📊 D3.js Specialist)

*   **Core Purpose:** Specializes in creating dynamic, interactive data visualizations for the web using D3.js, focusing on best practices, accessibility, and performance
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-diagramer` (❓ 📊 Diagramer)

*   **Core Purpose:** A specialized mode focused on translating conceptual descriptions into Mermaid syntax for various diagram types (flowcharts, sequence, class, state, ERD, etc.)
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-mui` (❓ 🎨 MUI Specialist)

*   **Core Purpose:** Implements UIs using the Material UI (MUI) ecosystem (Core, Joy, Base) for React, focusing on components, theming, styling (`sx`, `styled`), and Material Design principles
*   **Key Capabilities:** Implementation, Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-one-shot` (❓ ✨ One Shot Web Designer)

*   **Core Purpose:** Rapidly creates beautiful, creative web page visual designs (HTML/CSS/minimal JS) in a single session, focusing on aesthetic impact and delivering high-quality starting points
*   **Key Capabilities:** Design, Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-shadcn` (❓ 🧩 Shadcn UI Specialist)

*   **Core Purpose:** Specializes in building UIs using Shadcn UI components with React and Tailwind CSS, focusing on composition, customization via CLI, and accessibility
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-tailwind` (❓ 💨 Tailwind CSS Specialist)

*   **Core Purpose:** Implements modern, responsive UIs using Tailwind CSS, with expertise in utility classes, configuration customization, responsive design, and optimization for production
*   **Key Capabilities:** Implementation, Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-threejs` (❓ 🧊 Three.js Specialist)

*   **Core Purpose:** Specializes in creating 3D graphics and animations for the web using Three.js, including scene setup, materials, lighting, models (glTF), shaders (GLSL), and performance optimization
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `design-ui` (❓ 🎨 UI Designer)

*   **Core Purpose:** Creates aesthetically pleasing and functional user interfaces, focusing on UX, visual design, wireframes, mockups, prototypes, and style guides while ensuring responsiveness and accessibility
*   **Key Capabilities:** Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `dev-api` (❓ 🔌 API Developer)

*   **Core Purpose:** Expert worker mode for designing, implementing, testing, documenting, and securing APIs (RESTful, GraphQL, etc.)
*   **Key Capabilities:** Implementation, Design, Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-backend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-backend`, `manager-project`
    *   **Frequent Collaborators:** `lead-backend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `dev-core-web` (❓ ⌨️ Core Web Developer)

*   **Core Purpose:** Implements foundational UI and interactions using core web technologies: semantic HTML, modern CSS, and vanilla JavaScript (ES6+)
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `dev-eslint` (❓ 📏 ESLint Specialist)

*   **Core Purpose:** Responsible for implementing sophisticated linting solutions using ESLint's modern configuration system
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `dev-fixer` (❓ 🩺 Bug Fixer)

*   **Core Purpose:** Expert software debugger specializing in systematic problem diagnosis and resolution
*   **Key Capabilities:** Debugging
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `dev-git` (❓ 🦕 Git Manager)

*   **Core Purpose:** Executes Git commands safely and accurately based on instructions
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `dev-react` (❓ ⚛️ React Specialist)

*   **Core Purpose:** Specializes in building modern React applications using functional components, hooks, state management, performance optimization, and TypeScript integration
*   **Key Capabilities:** Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `dev-solver` (❓ 🧩 Complex Problem Solver)

*   **Core Purpose:** Systematically analyzes complex problems, identifies root causes, explores solutions, and provides actionable recommendations
*   **Key Capabilities:** Analysis
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `edge-workers` (❓ ⚡ Cloudflare Workers Specialist)

*   **Core Purpose:** Specialized worker for developing and deploying Cloudflare Workers applications, including edge functions, service bindings (KV, R2, D1, Queues, DO, AI), asset management, Wrangler configuration, and performance optimization
*   **Key Capabilities:** Implementation, Coordination, Infrastructure
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-angular` (❓ 🅰️ Angular Developer)

*   **Core Purpose:** Expert in developing robust, scalable, and maintainable Angular applications using TypeScript, with a focus on best practices, performance, testing, and integration with Angular ecosystem tools
*   **Key Capabilities:** Implementation, Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-astro` (❓ 🧑‍🚀 Astro Developer)

*   **Core Purpose:** Specializes in building fast, content-focused websites and applications with the Astro framework, focusing on island architecture, content collections, integrations, performance, SSR, and Astro DB/Actions
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-django` (❓ 🐍 Django Developer)

*   **Core Purpose:** Specializes in building secure, scalable, and maintainable web applications using the high-level Python web framework, Django
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-backend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-backend`, `manager-project`
    *   **Frequent Collaborators:** `lead-backend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-fastapi` (❓ 💨 FastAPI Developer)

*   **Core Purpose:** Expert in building modern, fast (high-performance) web APIs with Python 3.7+ using FastAPI
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-backend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-backend`, `manager-project`
    *   **Frequent Collaborators:** `lead-backend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-flask` (❓ 🧪 Flask Developer)

*   **Core Purpose:** Expert in building robust web applications and APIs using the Flask Python microframework
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-backend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-backend`, `manager-project`
    *   **Frequent Collaborators:** `lead-backend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-frappe` (❓ 🛠️ Frappe Specialist)

*   **Core Purpose:** Implements sophisticated solutions using the Frappe Framework, including DocTypes, Controllers, Server Scripts, Client Scripts, Permissions, Workflows, and Bench commands
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-laravel` (❓ 🐘 PHP/Laravel Developer)

*   **Core Purpose:** Builds and maintains web applications using PHP and the Laravel framework, including Eloquent, Blade, Routing, Middleware, Testing, and Artisan
*   **Key Capabilities:** Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-backend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-backend`, `manager-project`
    *   **Frequent Collaborators:** `lead-backend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-nextjs` (❓ 🚀 Next.js Developer)

*   **Core Purpose:** Expert in building efficient, scalable full-stack web applications using Next.js, specializing in App Router, Server/Client Components, advanced data fetching, Server Actions, rendering strategies, API routes, Vercel deployment, and performance optimization.

*   **Key Capabilities:** Infrastructure
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-remix` (❓ 💿 Remix Developer)

*   **Core Purpose:** Expert in developing fast, resilient, full-stack web applications using Remix, focusing on routing, data flow, progressive enhancement, and server/client code colocation
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-sveltekit` (❓ 🔥 SvelteKit Developer)

*   **Core Purpose:** Specializes in building high-performance web applications using the SvelteKit framework, covering routing, data loading, form handling, SSR/SSG, and deployment
*   **Key Capabilities:** Infrastructure
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `framework-vue` (❓ 💚 Vue.js Developer)

*   **Core Purpose:** Expertly builds modern, performant UIs and SPAs using Vue.js (v2/v3), Composition API, Options API, Vue Router, and Pinia/Vuex
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `infra-compose` (❓ 🐳 Docker Compose Specialist)

*   **Core Purpose:** Expert in designing, building, securing, and managing containerized applications with a focus on Docker Compose, Dockerfiles, and orchestration best practices
*   **Key Capabilities:** Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-devops`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-devops`, `manager-project`
    *   **Frequent Collaborators:** `lead-devops`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `infra-specialist` (❓ 🏗️ Infrastructure Specialist)

*   **Core Purpose:** Designs, implements, manages, and secures cloud/on-prem infrastructure using IaC (Terraform, CloudFormation, etc.), focusing on reliability, scalability, cost-efficiency, and security
*   **Key Capabilities:** Implementation, Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-devops`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-devops`, `manager-project`
    *   **Frequent Collaborators:** `lead-devops`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `lead-backend` (❓ ⚙️ Backend Lead)

*   **Core Purpose:** Coordinates backend development (APIs, logic, data integration), manages workers, ensures quality, security, performance, and architectural alignment
*   **Key Capabilities:** Implementation, Coordination, Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `manager-project`, `core-architect`
    *   **Typical Reports To:** `roo-commander`, `manager-project`
    *   **Frequent Collaborators:** `core-architect`, `other lead-*`, `relevant spec-*`, `relevant agent-*`

---

---

### `lead-db` (🗄️ 🗄️ Database Lead)

*   **Core Purpose:** Coordinates database tasks (schema design, migrations, query optimization, security), manages workers, ensures data integrity and performance
*   **Key Capabilities:** Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `manager-project`, `core-architect`
    *   **Typical Reports To:** `roo-commander`, `manager-project`
    *   **Frequent Collaborators:** `core-architect`, `other lead-*`, `relevant spec-*`, `relevant agent-*`

---

---

### `lead-design` (🎨 🎨 Design Lead)

*   **Core Purpose:** Coordinates design tasks (UI, diagrams), manages design workers, ensures quality and consistency, and reports progress to Directors
*   **Key Capabilities:** Design, Coordination, Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `manager-project`, `core-architect`
    *   **Typical Reports To:** `roo-commander`, `manager-project`
    *   **Frequent Collaborators:** `core-architect`, `other lead-*`, `relevant spec-*`, `relevant agent-*`

---

---

### `lead-devops` (🚀 🚀 DevOps Lead)

*   **Core Purpose:** Coordinates DevOps tasks (CI/CD, infra, containers, monitoring, deployment), manages workers, ensures operational stability and efficiency
*   **Key Capabilities:** Coordination, Infrastructure
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `manager-project`, `core-architect`
    *   **Typical Reports To:** `roo-commander`, `manager-project`
    *   **Frequent Collaborators:** `core-architect`, `other lead-*`, `relevant spec-*`, `relevant agent-*`

---

---

### `lead-frontend` (🖥️ 🖥️ Frontend Lead)

*   **Core Purpose:** Coordinates frontend development tasks, manages frontend workers, ensures code quality, performance, and adherence to design/architecture
*   **Key Capabilities:** Implementation, Design, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `manager-project`, `core-architect`
    *   **Typical Reports To:** `roo-commander`, `manager-project`
    *   **Frequent Collaborators:** `core-architect`, `other lead-*`, `relevant spec-*`, `relevant agent-*`

---

---

### `lead-qa` (💎 💎 QA Lead)

*   **Core Purpose:** The QA Lead is responsible for coordinating and overseeing all quality assurance activities within the project. They ensure that software releases meet quality standards by planning, delegating, and monitoring testing efforts. They receive features ready for testing or high-level quality objectives from Directors (e.g., Project Manager) or other Leads (e.g., Frontend Lead, Backend Lead) and translate them into actionable testing tasks for the QA Worker modes. Their primary goals are to ensure thorough test coverage, facilitate effective bug detection and reporting, assess product quality, and communicate quality-related risks.

*   **Key Capabilities:** Coordination, Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `manager-project`, `core-architect`
    *   **Typical Reports To:** `roo-commander`, `manager-project`
    *   **Frequent Collaborators:** `core-architect`, `other lead-*`, `relevant spec-*`, `relevant agent-*`

---

---

### `lead-security` (❓ 🛡️ Security Lead)

*   **Core Purpose:** Coordinates security strategy, risk management, compliance, incident response, and manages security specialists
*   **Key Capabilities:** Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`, `manager-project`, `core-architect`
    *   **Typical Reports To:** `roo-commander`, `manager-project`
    *   **Frequent Collaborators:** `core-architect`, `other lead-*`, `relevant spec-*`, `relevant agent-*`

---

---

### `manager-onboarding` (❓ 🚦 Project Onboarding)

*   **Core Purpose:** Handles initial user interaction, determines project scope (new/existing), delegates discovery/requirements gathering, coordinates basic setup, and delegates tech initialization
*   **Key Capabilities:** Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `lead-*`, `core-architect`

---

---

### `manager-product` (❓ 📦 Product Manager)

*   **Core Purpose:** A strategic director-level mode responsible for defining and executing product vision, strategy, and roadmap. Translates business goals and user needs into actionable product requirements, coordinates with technical teams, and ensures product success through data-driven decision making
*   **Key Capabilities:** Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `lead-*`, `core-architect`

---

---

### `manager-project` (❓ 📋 Project Manager (MDTM))

*   **Core Purpose:** Manages project features/phases using the TOML-based Markdown-Driven Task Management (MDTM) system, breaking down work, delegating tasks, tracking status, and reporting progress. Operates primarily within the `.ruru/tasks/` directory
*   **Key Capabilities:** Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `lead-*`, `core-architect`

---

---

### `prime-coordinator` (❓ 🚜 Prime Coordinator)

*   **Core Purpose:** Directly orchestrates development tasks AND Roo Commander configuration changes. Assumes user provides clear instructions. Uses staging for protected core files
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `roo-commander`

---

---

### `prime-dev` (❓ 🐹 Prime Dev)

*   **Core Purpose:** Edits structured configuration files (e.g., *.mode.md TOML, *.js, *.toml) directly in operational directories based on instructions from Prime Coordinator, respecting file access controls
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `roo-commander`

---

---

### `prime-txt` (❓ ✒️ Prime Documenter)

*   **Core Purpose:** Edits Markdown content (rules, KB files, documentation) directly in operational directories based on instructions from the Prime Coordinator, requiring confirmation before saving
*   **Key Capabilities:** Writing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `roo-commander`
    *   **Typical Reports To:** `roo-commander`
    *   **Frequent Collaborators:** `roo-commander`

---

---

### `roo-commander` (❓ 👑 Roo Commander)

*   **Core Purpose:** Highest-level coordinator for software development projects, managing goals, delegation, and project state
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `user`
    *   **Typical Reports To:** `user`
    *   **Frequent Collaborators:** `prime-coordinator`, `lead-*`, `manager-*`, `core-architect`

---

---

### `spec-crawl4ai` (❓ 🕷️ Crawl4AI Specialist)

*   **Core Purpose:** Implements advanced web crawling solutions using the crawl4ai Python package, focusing on async execution, content extraction, filtering, and browser automation
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `spec-firecrawl` (❓ 🚒 Firecrawl Specialist)

*   **Core Purpose:** Implements web crawling and content extraction solutions using the Firecrawl service/API, focusing on configuration, job management, and data retrieval
*   **Key Capabilities:** Implementation, Coordination
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `spec-huggingface` (🤗 🤗 Hugging Face Specialist)

*   **Core Purpose:** Implements solutions using Hugging Face Hub models and libraries (Transformers, Diffusers, Datasets, etc.) for various AI/ML tasks including natural language processing, computer vision, audio processing, and generative AI. Specializes in model selection, inference implementation, data preprocessing, and integration with application code
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `spec-openai` (🎱 🎱 OpenAI Specialist)

*   **Core Purpose:** Implements solutions using OpenAI APIs (GPT models, Embeddings, DALL-E, etc.), focusing on prompt engineering and API integration. Specializes in selecting appropriate models, crafting effective prompts, and integrating OpenAI services securely and efficiently into applications
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-unknown`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-unknown`, `manager-project`
    *   **Frequent Collaborators:** `lead-unknown`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `test-e2e` (❓ 🎭 E2E Testing Specialist)

*   **Core Purpose:** Designs, writes, executes, and maintains End-to-End (E2E) tests using frameworks like Cypress, Playwright, Selenium to simulate user journeys and ensure application quality
*   **Key Capabilities:** Design, Testing, Writing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-qa`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-qa`, `manager-project`
    *   **Frequent Collaborators:** `lead-qa`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `test-integration` (❓ 🔗 Integration Tester)

*   **Core Purpose:** Verifies interactions between components, services, or systems, focusing on interfaces, data flow, and contracts using API testing, mocks, and stubs
*   **Key Capabilities:** Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-qa`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-qa`, `manager-project`
    *   **Frequent Collaborators:** `lead-qa`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-accessibility` (❓ ♿ Accessibility Specialist)

*   **Core Purpose:** Audits UIs, implements fixes (HTML, CSS, ARIA), verifies WCAG compliance, generates reports, and guides teams on accessible design patterns
*   **Key Capabilities:** Implementation, Design, Debugging
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-jquery` (❓ 🎯 jQuery Specialist)

*   **Core Purpose:** Specializes in implementing and managing jQuery-based applications, focusing on efficient DOM manipulations, handling events, AJAX calls, plugin integration, and managing jQuery modules, while adhering to modern JavaScript practices where applicable
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-junior-dev` (❓ 🌱 Junior Developer)

*   **Core Purpose:** Assists with well-defined, smaller coding tasks under supervision, focusing on learning and applying basic development practices
*   **Key Capabilities:** Implementation
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-mode-maintainer` (❓ 🔧 Mode Maintainer)

*   **Core Purpose:** Applies specific, instructed modifications to existing custom mode definition files (`*.mode.md`), focusing on accuracy and adherence to the TOML+Markdown format
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-performance` (❓ ⚡ Performance Optimizer)

*   **Core Purpose:** Identifies, analyzes, and resolves performance bottlenecks across the application stack (frontend, backend, database) and infrastructure
*   **Key Capabilities:** Analysis, Infrastructure
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-refactor` (❓ ♻️ Refactor Specialist)

*   **Core Purpose:** Improves the internal structure, readability, maintainability, and potentially performance of existing code without changing its external behavior
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-reviewer` (❓ 👀 Code Reviewer)

*   **Core Purpose:** Meticulously reviews code changes for quality, standards, maintainability, and correctness
*   **Key Capabilities:** Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-second-opinion` (❓ 🤔 Second Opinion)

*   **Core Purpose:** Provides an independent, critical evaluation of proposed solutions, designs, code changes, or technical decisions, focusing on identifying potential risks, alternatives, and trade-offs
*   **Key Capabilities:** Design
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-senior-dev` (❓ 🧑‍💻 Senior Developer)

*   **Core Purpose:** Designs, implements, and tests complex software components and features, applying advanced technical expertise, mentoring junior developers, and collaborating across teams
*   **Key Capabilities:** Implementation, Design, Testing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-typescript` (❓ 🔷 TypeScript Specialist)

*   **Core Purpose:** Specializes in writing, configuring, and improving strongly-typed JavaScript applications using TypeScript
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-vite` (❓ ⚡ Vite Specialist)

*   **Core Purpose:** Expert in configuring, optimizing, and troubleshooting frontend tooling using Vite, including dev server, production builds, plugins, SSR, library mode, and migrations
*   **Key Capabilities:** Core Task Execution
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

---

### `util-writer` (❓ ✍️ Technical Writer)

*   **Core Purpose:** Creates clear, accurate, and comprehensive documentation tailored to specific audiences, including READMEs, API documentation, user guides, and tutorials
*   **Key Capabilities:** Writing
*   **Hierarchy & Collaboration:**
    *   **Typical Delegators:** `lead-frontend`, `manager-project`, `roo-commander`
    *   **Typical Reports To:** `lead-frontend`, `manager-project`
    *   **Frequent Collaborators:** `lead-frontend`, `other spec-*`, `relevant agent-*`, `core-architect`

---

## 4. Maintaining This Guide

The detailed mode information in Section 3 should be kept up-to-date automatically. A dedicated build script (e.g., `build_mode_selection_guide_data.js` - **Task TBD**) is responsible for parsing all `.mode.md` files and regenerating the content for Section 3. Manual edits to Section 3 are discouraged as they will be overwritten.