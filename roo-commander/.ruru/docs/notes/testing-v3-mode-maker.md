lets see if we can make a 'bun' mode

now its using the vertex ai mcp for research....

its saving the research ingo the docs/vertex folder in suitable sub folders

this should make it easier to refer back to later

we picked the deep dive option so it should rightly be doing a lot of research, and bun is a fairly big topic area

the 'standard' option is a lot less comprehensive than this

i dont really know how long this will go on or how much it will do, its all driven by the ai deciding what is appropriate

not sure if this is going to turn out quite like i expected tbh

ill let it run anway and see

ok good thats the modes.md file

looks ok

curious to see what it actually puts in the kb folder../.

i probabkly should have picked a different emoji, like an actual bun.... 

not sure why it asked for permission, i think its because its using the oh its because we are using prime documenter, it is a more cautios writer

seems ok but it still didnt make much detailed context in the kb hrm.....

ok time to make a new and improved workflow.....