+++
# --- Basic Metadata ---
id = "KB-RC-COMMUNITY-JOIN"
title = "Community & Contributing: Joining the Community"
status = "draft"
doc_version = "1.0" # Version of this guide
content_version = 1.0
audience = ["users", "developers", "contributors"]
last_reviewed = "2025-04-28" # Use current date
template_schema_doc = ".ruru/templates/toml-md/10_guide_tutorial.README.md" # Using guide template schema
tags = ["roo-commander", "community", "support", "discord", "collaboration", "getting-started", "guide"]
related_docs = [
    "../README.md", # Link to the KB README
    "02_Contribution_Guide.md",
    "../../../../.ruru/modes/roo-commander/kb/initial-actions/15-join-community.md" # Link to the source KB action file
    ]
difficulty = "beginner"
estimated_time = "~5 minutes"
prerequisites = ["Interest in connecting with other Roo Commander users/developers"]
learning_objectives = ["Find the primary community channel (Discord)", "Understand the benefits of joining the community"]
+++

# Community & Contributing: Joining the Community

## 1. Introduction / Goal 🎯

The Roo Commander framework benefits greatly from an active community of users and contributors. Joining the community provides opportunities to:

*   Ask questions and get support from other users and developers.
*   Share your experiences, custom modes, or workflows.
*   Discuss best practices and advanced techniques.
*   Stay updated on the latest developments and releases.
*   Collaborate on improving the framework.

This guide points you to the primary channels for connecting with the Roo Commander community.

## 2. Primary Community Channel: Discord 💬

The main hub for real-time discussion, support, and collaboration is the official **Roo Commander Discord server**.

*   **How to Join:**
    1.  You can ask Roo Commander directly! Select **Option 15: 🐾 Join the Roo Commander Community (Discord)** from the initial interaction prompt, or simply ask:
        ```prompt
        How can I join the Roo Commander Discord community?
        ```
    2.  Roo Commander will retrieve the invitation link and instructions from its knowledge base (`.ruru/modes/roo-commander/kb/initial-actions/15-join-community.md`).
    3.  Follow the provided Discord invitation link.
*   **What to Expect:** Different channels for general discussion, support, showcasing projects, development talk, mode sharing, and announcements.

## 3. Other Community Platforms (Check Discord for Updates) 🌐

While Discord is the primary hub, other platforms may be used:

*   **GitHub Repository:** [https://github.com/jezweb/roo-commander](https://github.com/jezweb/roo-commander)
    *   **Issues:** For reporting bugs and suggesting specific features (see `02_Contribution_Guide.md`).
    *   **Discussions:** May be enabled for broader questions, ideas, and Q&A. Check the repository tabs.
*   **Forums/Mailing Lists:** Check the Discord server or project documentation for any official forums or mailing lists that may be established.

## 4. Community Etiquette 🙏

*   Be respectful and constructive in your interactions.
*   Search existing channels/discussions before asking common questions.
*   Provide sufficient context when asking for help (e.g., what you tried, error messages, relevant configuration).
*   Share your knowledge and help others when you can.

## 5. Conclusion ✅

Connecting with the Roo Commander community is a great way to enhance your experience, get help when needed, and contribute back to the project. The Discord server is the primary place to start. We look forward to seeing you there!