> **🚧 Work in Progress:** The IntelliManage features described in this section are currently under development and not yet available for general use. This documentation reflects planned functionality.
# 05 Using IntelliManage Features

This section details how to use the planned IntelliManage features within Roo Commander, covering artifact management, methodologies, dependencies, reporting, and integrations.

## Files in this section

*   [01 Creating Managing Artifacts](01_Creating_Managing_Artifacts.md)
*   [02 Working with Methodologies](02_Working_with_Methodologies.md)
*   [03 Linking Dependencies](03_Linking_Dependencies.md)
*   [04 Reporting Visualization](04_Reporting_Visualization.md)
*   [05 GitHub Integration](05_GitHub_Integration.md)

[Back to Main KB README](../README.md)