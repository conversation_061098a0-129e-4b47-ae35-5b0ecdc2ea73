+++
# --- Basic Metadata ---
id = "KB-RC-GUIDE-README"
title = "Roo Commander Knowledge Base: README"
status = "draft"
doc_version = "1.0"
content_version = 1.0
audience = ["users", "developers", "architects", "contributors", "ai_modes"]
last_reviewed = "2025-04-28" # Use current date
template_schema_doc = ".ruru/templates/toml-md/09_documentation.README.md"
tags = ["intellimanage", "roo-commander", "knowledge-base", "readme", "guide", "documentation", "index"]
related_docs = ["README.md"] # Link to main project README
+++

# Roo Commander Knowledge Base: README

## 1. Introduction / Purpose 🎯

Welcome to the comprehensive Knowledge Base (KB) for the **Roo Commander** framework and its integration with the **IntelliManage** project management system.

This KB serves as the central repository for documentation covering:
*   Core concepts and architecture of Roo Commander and IntelliManage.
*   Detailed explanations of workflows, processes, and standards.
*   Guides for getting started and using specific features.
*   Reference material for modes, commands, and configurations.
*   Best practices for effective usage.

The goal is to provide a clear, structured, and accessible source of truth for both **human users** (developers, project managers, architects) and **AI agents** operating within the Roo Code environment.

## 2. Target Audience 👥

This Knowledge Base is intended for:
*   Users interacting with Roo Commander and IntelliManage.
*   Developers contributing to or customizing the framework.
*   AI Agents (including Roo Commander itself and specialist modes) needing reference information and procedural guidance.

## 3. How to Use This Knowledge Base 🧭

*   **Start Here:** This README provides an overview of the KB structure.
*   **Navigate Sections:** Use the section overview below to find documentation relevant to your needs.
*   **New Users:** Begin with the `01_Introduction/` and `03_Getting_Started/` sections.
*   **Understanding Concepts:** Refer to `02_Core_Concepts/` for foundational explanations.
*   **Specific Features:** Look under `04_Understanding_Modes/` <!-- or `05_Using_IntelliManage_Features/` -->.
*   **Customization:** Consult `06_Advanced_Usage_Customization/`.
*   **AI Agents:** Utilize the structured content and linked documents for context retrieval and operational guidance. Follow specific KB lookup rules defined in `.roo/rules-*/`.

## 4. KB Structure Overview 🗺️

This Knowledge Base is organized into the following main sections:

*   **`01_Introduction/`**: High-level overview of Roo Commander, its core principles, architecture, and integration with IntelliManage.
*   **`02_Core_Concepts/`**: Detailed explanations of fundamental building blocks like the file system structure, TOML+MD format, MDTM, Rules, KBs, IntelliManage artifacts, and multi-project support.
*   **`03_Getting_Started/`**: Guides for installing Roo Commander, understanding the initial interaction flow, basic examples, and session management concepts.
*   **`04_Understanding_Modes/`**: Information on the different mode roles/classifications, the mode hierarchy, the mode selection guide, and a reference list of all available modes.
<!-- *   **`05_Using_IntelliManage_Features/`**: Practical guides on using IntelliManage commands (`!pm ...`) for creating/managing artifacts, working with different methodologies, linking items, reporting, and GitHub integration. -->
*   **`06_Advanced_Usage_Customization/`**: Documentation on creating custom modes, writing custom instructions/rules, configuring user preferences, and using the `prime-coordinator`.
*   **`07_Best_Practices_Troubleshooting/`**: Tips for effective prompting, context management, troubleshooting common issues, and reviewing AI output.
*   **`08_Community_Contributing/`**: Information on joining the community and contributing to the project.
*   **`09_Reference/`**: Glossary of terms and the full, potentially generated, list of modes.

## 5. Key Concepts & Getting Started Links 🚀

*   For a foundational understanding of how Roo Commander and IntelliManage work, start with **`02_Core_Concepts/`**.
*   If you're new to Roo Commander and want to set it up or see a basic example, begin with **`03_Getting_Started/`**.

## 6. Contributing & Feedback 🤝

Contributions and feedback to improve this Knowledge Base are welcome! Please refer to the Contribution Guide (`08_Community_Contributing/02_Contribution_Guide.md`) for details on how to suggest changes or report issues.

## 7. Related Documentation 🔗

*   [Main Project README](.ruru/docs/guides/README.md)