# 02 Core Concepts

This section explains the fundamental concepts and standards used within the Roo Commander project, such as file structure, task management, and knowledge organization.

## Files in this section

*   [01 File System Structure](01_File_System_Structure.md)
*   [02 TOML MD Standard](02_TOML_MD_Standard.md)
*   [03 MDTM Explained](03_MDTM_Explained.md)
*   [04 Rules Explained](04_Rules_Explained.md)
*   [05 Knowledge Bases Explained](05_Knowledge_Bases_Explained.md)
*   [06 IntelliManage Artifacts](06_IntelliManage_Artifacts.md)
*   [07 Multi Project Workspaces](07_Multi_Project_Workspaces.md)

[Back to Main KB README](../README.md)