+++
# --- Metadata ---
id = "PLAYBOOK-DEMO-GENART-V1"
title = "Capability Playbook: Generative Art Web Page"
status = "draft" # Start as draft until tested
created_date = "2025-04-24"
updated_date = "2025-04-24"
version = "1.0"
tags = ["playbook", "documentation", "capability-demo", "generative-art", "ai-image", "openai", "dall-e", "frontend", "animation", "animejs", "epic", "feature", "task"]
related_docs = [
    ".ruru/docs/standards/project-management-strategy-v1.md",
    ".ruru/planning/project-structure/00-epic-feature-task-plan.md",
    ".ruru/modes/spec-openai/spec-openai.mode.md", # Assumes this mode can call DALL-E
    ".ruru/modes/design-animejs/design-animejs.mode.md",
    ".ruru/modes/dev-general/dev-general.mode.md",
    ".ruru/modes/design-tailwind/design-tailwind.mode.md" # Example styling
]
objective = "Guide the creation of a visually unique web page where background images or textures are generated by an AI image model (e.g., DALL-E via `spec-openai`) and potentially animated, showcasing creative AI integration."
scope = "Covers defining the artistic theme, generating AI assets, integrating them into a web page structure, adding optional animations, and basic setup."
target_audience = ["Users", "Developers", "Designers", "AI Modes"]
# --- Document Specific Fields ---
example_project_type = "Creative Web Demo / Portfolio Piece"
ai_image_provider_placeholder = "[AI Image Provider]" # e.g., "OpenAI DALL-E 3", "Stable Diffusion"
image_style_placeholder = "[Artistic Style/Theme]" # e.g., "Abstract Watercolor", "Cyberpunk Cityscape", "Surreal Organic Textures"
animation_library_placeholder = "anime.js" # Example
+++

# Capability Playbook: Generative Art Web Page

This playbook demonstrates how Roo Commander can orchestrate the creation of a unique web page using AI-generated visuals and frontend animations.

**Scenario:** You want to create a single web page with a specific artistic theme (e.g., `[Artistic Style/Theme]`), using images generated by `[AI Image Provider]` for backgrounds or key visual elements, potentially animated with `[animation_library_placeholder]`.

## Phase 1: Concept & Setup

1.  **Define the Vision (Epic/Feature):**
    *   **Goal:** Establish the artistic direction, the role of the AI imagery, and the overall feel of the page.
    *   **Action:** Create an Epic or Feature (e.g., `.ruru/features/FEAT-160-generative-[image_style_placeholder]-webpage.md`).
    *   **Content:** Define the `objective` (e.g., "Create a single-page web experience with an animated background generated by DALL-E 3 in a surreal organic texture style"), `scope_description` (single page, focus on visual/animation, minimal text content), desired `[Artistic Style/Theme]`. Set `status` to "Planned".

2.  **Basic Web Page Setup (Feature):**
    *   **Goal:** Create the minimal HTML/CSS/JS structure to host the art and animations.
    *   **Action:** Define as a Feature (`FEAT-161-genart-page-setup.md`). Delegate tasks to `dev-general`.
    *   **Tasks (Examples):**
        *   "Create basic `index.html` with standard head, body, and a main container div (e.g., `#art-container`)."
        *   "Create basic `style.css` with reset/normalization and initial body/container styles."
        *   "Create empty `script.js` and link it in `index.html`."
        *   "Create an `assets/generated/` directory."
    *   **Process:** Use MDTM workflow. Mark Feature "Done" when the blank page structure is ready.

## Phase 2: AI Image Generation

1.  **Plan Image Generation (Feature):**
    *   **Goal:** Define the specific prompts and parameters for the AI image generation.
    *   **Action:** Define as a Feature (`FEAT-162-define-ai-image-prompts.md`). Requires significant User input, potentially refined by `spec-openai` or `agent-research`.
    *   **Tasks (Examples):**
        *   "Draft 3-5 detailed text prompts for `[AI Image Provider]` reflecting the `[Artistic Style/Theme]`." (User/Coordinator task via `ask_followup_question`).
        *   "Specify image parameters (e.g., aspect ratio 16:9, number of images per prompt, quality/style settings)." (User/Coordinator task).
        *   *(Optional)* "Refine prompts for clarity and effectiveness using `spec-openai`'s prompt knowledge." (Delegate prompt review).
    *   **Output:** Finalized prompts and parameters documented in the Feature file. Set `status` to "Ready for Dev".

2.  **Execute Image Generation & Selection:**
    *   **Goal:** Generate images using the defined prompts and select the best ones.
    *   **Action:** Delegate generation task to `spec-openai` (assuming it handles the API call and returns image URLs or potentially file paths if it saves them). Selection is likely a User task.
    *   **Tasks (Examples):**
        *   "Execute image generation using `[AI Image Provider]` with the defined prompts and parameters from FEAT-162." (Delegate to `spec-openai`). **Requires User's API Key configured securely in environment/mode settings - DO NOT PASS KEYS IN MESSAGES.**
        *   "Present the generated image URLs (or paths if saved locally by the specialist) to the user for selection." (Coordinator task based on specialist output).
        *   "User selects [N] images to use." (User interaction via `ask_followup_question` with image previews/links).
        *   "Record the URLs/paths of the selected images." (Coordinator task, update Feature FEAT-162 or create a new artifact).
    *   **Process:** Use MDTM workflow. Be mindful of potential API costs and generation times.

## Phase 3: Integration & Frontend Development

1.  **Integrate Generated Images (Feature):**
    *   **Goal:** Place the selected AI-generated images into the HTML/CSS structure.
    *   **Action:** Define as a Feature (`FEAT-163-integrate-ai-images.md`). Delegate tasks to `dev-general` or `design-tailwind`.
    *   **Tasks (Examples):**
        *   "Read the selected image URLs/paths from FEAT-162."
        *   "Update `style.css`: Apply selected image(s) as CSS background(s) (e.g., on `body` or `#art-container`), potentially using multiple backgrounds or layers."
        *   "Alternatively, update `index.html`: Add `img` tags referencing the selected images if they are foreground elements."
        *   "Adjust CSS for proper image display (size, position, repeat, blend modes)."
    *   **Process:** Use MDTM workflow. Requires the results from Phase 2.

2.  **Add Complementary Content/Styling (Optional Tasks):**
    *   **Goal:** Add minimal text or other UI elements that complement the generative art.
    *   **Action:** Define Tasks under Feature FEAT-163 or a new one. Delegate to `dev-general`, `design-tailwind`.
    *   **Tasks (Examples):** "Add a simple title heading.", "Apply basic layout styles using Flexbox/Grid."

## Phase 4: Animation (Optional)

1.  **Define Animation Concept (Feature):**
    *   **Goal:** Describe the desired animation effect related to the theme/images.
    *   **Action:** Define as a Feature (`FEAT-164-implement-art-animation.md`). Requires User input/creative direction.
    *   **Content:** Describe the `[Animation Effect]` (e.g., "Subtly animate background position", "Fade elements in/out", "React to mouse movement").

2.  **Implement Animation (Tasks):**
    *   **Goal:** Code the animation using the chosen library.
    *   **Action:** Decompose Feature FEAT-164. Delegate tasks to `design-animejs`.
    *   **Tasks (Examples):**
        *   "Install `[animation_library_placeholder]` dependency or link CDN." (Coordinator task via `execute_command`).
        *   "In `script.js`, select target HTML elements for animation."
        *   "Implement the `[Animation Effect]` using `[animation_library_placeholder]` syntax."
        *   "Fine-tune animation parameters (duration, easing, delay)."
    *   **Process:** Use MDTM workflow.

## Phase 5: Finalization

1.  **Testing & Review:**
    *   **Goal:** Ensure the page loads correctly, visuals appear as intended, and animations (if any) are smooth.
    *   **Action:** Primarily manual browser testing by the User. Check different screen sizes if responsiveness is a goal.

2.  **Documentation (README):**
    *   **Goal:** Explain the demo project, the AI tools/prompts used, and how to run it.
    *   **Action:** Define Task. Delegate to `util-writer`.
    *   **Process:** Create/update `README.md`. Include screenshots/GIFs.

3.  **Complete Epic/Feature:** Mark relevant artifacts as "Done".

## Key Considerations for Generative Art Demos:

*   **AI Provider Setup:** Requires the user to have API keys and necessary configurations for the chosen `[AI Image Provider]` set up securely (e.g., via environment variables accessed by `spec-openai` or an MCP server). **Roo Commander should not handle API keys directly.**
*   **Prompt Engineering:** The quality of the generated images depends heavily on the prompts defined in Phase 2. Iteration might be needed.
*   **Image Handling:** Decide whether to use image URLs directly or download/save images locally (`assets/generated/`). Using URLs is simpler but relies on external hosting; saving locally gives more control but requires file management.
*   **Performance:** Large AI-generated images can impact page load time. Consider optimizing images (format, compression) as a potential refinement task (`util-performance`). Complex animations can impact runtime performance.
*   **Cost:** AI image generation APIs usually have associated costs. Be mindful of the number of images generated.
*   **Subjectivity:** "Good" generative art is subjective. User review and selection (Phase 2, Step 2) are critical.

This playbook embraces the creative potential of combining AI asset generation with web development tools, managed within the structured Roo Commander environment.