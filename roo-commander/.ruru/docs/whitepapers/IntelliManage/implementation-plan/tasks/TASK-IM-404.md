+++
# --- Task/Story/Bug Metadata ---
id = "TASK-IM-404"
title = "Implement session start/resume logic (handover check, prompts)"
status = "⚪️ Planned"
type = "🛠️ Task"
created_date = "2025-04-28" # Use current date
updated_date = "2025-04-28" # Use current date
project_name = "intellimanage_implementation"
feature_id = "FEAT-IM-004"
# epic_id = "EPIC-IM-001" # Implied via Feature
# assigned_to = "..." # Dev implementing session-manager rules/logic
# reporter = "..."
priority = "🔥 Highest" # Core session management functionality
# estimated_effort = "M" # Involves file I/O, logic, prompting
# due_date = "YYYY-MM-DD"
# sprint_id = "..."
tags = ["mode", "session-manager", "implementation", "session-state", "handover", "context", "rules"]
related_docs = ["RULES-SESSION-MANAGER-001", "MODE-SPEC-SESSION-MANAGER-001", "DATA-FORMAT-HANDOVER-001"]
depends_on = ["TASK-IM-402"] # Depends on the rules structure being in place
# related_commits = []
# related_prs = []
# related_issues = []
+++

# Task: Implement session start/resume logic (handover check, prompts)

## Description ✍️

Implement the core logic within the `session-manager` mode (likely within its rules defined in `TASK-IM-402`) to handle the session start and resume procedure as specified in `RULES-SESSION-MANAGER-001`, Section 2.

This involves:
1.  Checking the `.ruru/context/handovers/` directory for the latest handover summary file upon activation.
2.  Reading and parsing the summary file if found.
3.  Presenting the summary context and appropriate continuation prompts to the user via `ask_followup_question`.
4.  Handling the case where no summary is found (new session greeting and goal elicitation).
5.  Initializing the session log file.

## Acceptance Criteria ✅

*   - [ ] On activation, the `session-manager` mode correctly uses `list_files` (or equivalent) to check `.ruru/context/handovers/`.
*   - [ ] Logic correctly identifies the most recent `handover_*.md` file based on filename timestamp.
*   - [ ] If a recent summary exists, the mode uses `read_file` to load its content.
*   - [ ] The mode correctly parses key information (last goal, next step, active tasks) from the loaded summary.
*   - [ ] The mode uses `ask_followup_question` to present the summary context and prompt the user to continue or set a new goal.
*   - [ ] If no summary exists, the mode uses `ask_followup_question` to provide the standard new session greeting and ask for the initial goal.
*   - [ ] Upon establishing the initial goal (from summary or user input), the mode correctly generates a timestamped session log filename.
*   - [ ] The mode uses `write_to_file` (or equivalent) to create the session log in `.ruru/sessions/` with the correct initial entry.
*   - [ ] Error handling is implemented for cases where handover files exist but cannot be read or parsed.

## Implementation Notes / Details 📝

*   This logic will primarily be implemented within the Roo rules (`.roo` files) for `session-manager`.
*   Requires correct usage of tools: `list_files`, `read_file`, `ask_followup_question`, `write_to_file`.
*   Parsing the Markdown summary might involve simple string manipulation or regular expressions within the rules, or potentially delegating complex parsing to a utility function/mode if needed.
*   Ensure the prompts generated by `ask_followup_question` are clear and provide the suggested actions as defined in the rules.
*   Timestamp generation for the session log filename needs to be reliable.

## Subtasks / Checklist ☑️

*   - [ ] Implement rule logic to check for handover files using `list_files`.
*   - [ ] Implement rule logic to identify the latest handover file.
*   - [ ] Implement rule logic to read the latest handover file using `read_file`.
*   - [ ] Implement rule logic to parse key sections from the handover summary Markdown.
*   - [ ] Implement rule logic to formulate and execute the `ask_followup_question` prompt for session resumption.
*   - [ ] Implement rule logic to formulate and execute the `ask_followup_question` prompt for a new session.
*   - [ ] Implement rule logic to generate the session log filename.
*   - [ ] Implement rule logic to create the initial session log file using `write_to_file`.
*   - [ ] Add error handling for file operations (read/write).
*   - [ ] Add comments to the rule files explaining the start/resume logic.
*   - [ ] Test the session start logic with no handover file present.
*   - [ ] Test the session resume logic with a valid handover file present.
*   - [ ] Test the session resume logic with multiple handover files present (ensure latest is picked).
*   - [ ] Test error handling for unreadable handover files.