# --- Basic Metadata ---
id = "DATA-FORMAT-HANDOVER-001"
title = "IntelliManage: Handover Summary Data Format"
status = "draft"
doc_version = "1.0"
content_version = 1.0
audience = ["developers", "architects", "contributors", "ai_modes", "session-manager", "agent-session-summarizer"]
last_reviewed = "2025-04-28" # Use current date
template_schema_doc = ".ruru/templates/toml-md/09_documentation.README.md" # Using the guide template schema
tags = ["intellimanage", "specification", "data-format", "handover", "session-management", "context", "markdown"]
related_docs = [
    "DOC-ARCH-001",
    "DOC-FS-SPEC-001",
    "MODE-SPEC-SESSION-MANAGER-001",
    "MODE-SPEC-AGENT-SESSION-SUMMARIZER-001",
    ".ruru/templates/handover_summary_template.md" # Link to the template file
    ]
+++

# IntelliManage: Handover Summary Data Format

## 1. Introduction / Overview 🎯

This document specifies the standard format and content structure for **Handover Summary** files within the IntelliManage framework. These summaries are generated by the `agent-session-summarizer` mode and consumed primarily by the `session-manager` mode to facilitate session continuity and context resumption.

The goal is to provide a concise yet informative snapshot of the project state at the end of a work session, enabling users or AI agents to quickly understand the current goals, progress, pending tasks, and blockers.

## 2. File Location and Naming Convention 📍

*   **Directory:** Handover summaries **MUST** be stored in the `.ruru/context/handovers/` directory within the workspace root.
*   **Naming Convention:** Files **MUST** follow the pattern `handover_YYYYMMDD_HHMMSS.md`, where the timestamp represents the time the summary was generated.
    *   **Example:** `handover_20250428_153000.md`

## 3. File Format 📄

*   **Format:** Standard **Markdown (.md)**.
*   **Generation:** Generated by `agent-session-summarizer` based on the template located at `.ruru/templates/handover_summary_template.md`.
*   **TOML Frontmatter:** This document type **DOES NOT** typically include TOML frontmatter, as it's intended as a human/AI-readable summary rather than a machine-parsable artifact itself. Key metadata (like timestamp) is included in the filename and Markdown body.

## 4. Content Structure and Sections 🧱

The content **MUST** follow the structure defined by the template (`.ruru/templates/handover_summary_template.md`). Each section addresses a key aspect needed for handover:

1.  **Header Information:**
    *   `# Session Handover Summary` (Title)
    *   `**Generated:** {{TIMESTAMP}}`: The exact date and time (ISO 8601 format preferred, e.g., `YYYY-MM-DDTHH:MM:SSZ`) when the summary was generated.
        *   *Source:* System clock at time of generation.
    *   `**Context Window:** {{TOKEN_COUNT}} Tokens ({{PERCENTAGE}}%)`: Information about the context window size at the time of handover.
        *   *Source:* Provided by the requesting agent (e.g., `session-manager`).

2.  **`## 1. Current High-Level Goal(s)`:**
    *   **Content:** A bulleted list describing the primary objective(s) being pursued during the session that just ended.
    *   **Placeholder:** `{{CURRENT_GOAL}}`
    *   *Source:* Extracted from the session log (`.ruru/sessions/SESSION-....md`) or the active planning document referenced by the session manager.

3.  **`## 2. Last Key Action(s) Completed`:**
    *   **Content:** A bulleted list summarizing the most recent significant actions or task completions reported during the session.
    *   **Placeholder:** `{{LAST_ACTION_X}}`
    *   *Source:* Extracted from the session log. Focus on major milestones or completions reported by `roo-dispatch` or the CLE.

4.  **`## 3. Active / Pending Delegated Tasks`:**
    *   **Content:** A bulleted list of key tasks currently assigned to specialists and their status. Limit to a reasonable number (e.g., 3-7) of the most relevant active/pending tasks.
    *   **Format:** `**{{ACTIVE_TASK_X_ID}}:** {{ACTIVE_TASK_X_TITLE}} - **Status:** {{ACTIVE_TASK_X_STATUS}} - **Assigned:** {{ACTIVE_TASK_X_ASSIGNEE}}`
    *   **Placeholders:** `{{ACTIVE_TASK_X_ID}}`, `{{ACTIVE_TASK_X_TITLE}}`, `{{ACTIVE_TASK_X_STATUS}}`, `{{ACTIVE_TASK_X_ASSIGNEE}}`
    *   *Source:* Extracted from the session log (tracking delegations) or by querying/listing relevant task files (`.ruru/projects/[project_slug]/tasks/`) via the CLE based on status and recency.

5.  **`## 4. Next Planned Step(s)`:**
    *   **Content:** A bulleted list outlining the immediate next action(s) planned based on the current state.
    *   **Placeholder:** `{{NEXT_STEP_X}}`
    *   *Source:* Extracted from the active planning document or inferred from the last steps in the session log.

6.  **`## 5. Key Document Links`:**
    *   **Content:** Standardized links to essential context documents for easy navigation upon resuming.
    *   **Placeholders:**
        *   `{{COORDINATION_TASK_LINK}}`: Path to the relevant session log file (`.ruru/sessions/SESSION-....md`).
        *   `{{PLANNING_DOC_LINK}}`: Path to the primary planning document being worked against.
    *   *Source:* Provided by the requesting agent (`session-manager`) or inferred from context. Includes static links to Structure Inventory and Mode Hierarchy for general reference.

7.  **`## 6. Blockers / Open Questions`:**
    *   **Content:** A bulleted list of any known issues, dependencies waiting, or questions that need resolution to proceed.
    *   **Placeholders:** `{{BLOCKER_X}}`, `{{OPEN_QUESTION_X}}`
    *   *Source:* Extracted from session logs or task files marked as `"🚧 Blocked"`.

8.  **Concluding Note:**
    *   Standard text indicating the summary's purpose.

## 5. Example Handover Summary File

```markdown
# Session Handover Summary

**Generated:** 2025-04-28T16:45:00Z
**Context Window:** 45300 Tokens (55%)

## 1. Current High-Level Goal(s)

*   Implement user authentication feature (EPIC-002) for the 'backend-api' project.
*   *Source:* .ruru/sessions/SESSION-20250428-140000.md

## 2. Last Key Action(s) Completed

*   `roo-dispatch` reported successful implementation of TASK-031 (Create JWT generation utility) by `dev-python`.
*   `roo-dispatch` reported successful implementation of TASK-032 (Add `/login` endpoint structure) by `dev-api`.
*   *Source:* .ruru/sessions/SESSION-20250428-140000.md

## 3. Active / Pending Delegated Tasks

*   **TASK-033:** Implement password hashing logic - **Status:** 🔵 In Progress - **Assigned:** dev-python
*   **TASK-034:** Integrate JWT generation into /login endpoint - **Status:** 🟡 To Do - **Assigned:** dev-api
*   **TASK-035:** Write integration tests for /login - **Status:** 🟡 To Do - **Assigned:** test-integration
*   *Source:* Session Log / Task File Scan

## 4. Next Planned Step(s)

*   Monitor completion of TASK-033 (password hashing).
*   Delegate TASK-034 (JWT integration) to `dev-api` once TASK-033 is done.
*   *Source:* .ruru/planning/auth-feature-plan.md

## 5. Key Document Links

*   **Coordination Log:** .ruru/sessions/SESSION-20250428-140000.md
*   **Active Plan:** .ruru/planning/auth-feature-plan.md
*   **Structure Inventory:** `.docs/project_structure_inventory.md`
*   **Mode Hierarchy:** `.templates/mode_hierarchy.md`

## 6. Blockers / Open Questions

*   Need clarification from `core-architect` on preferred salt rounds for password hashing (Question logged in session log).

---
*This summary is intended to facilitate handover between sessions or instances.*
```

## 6. Generation and Consumption

*   **Generated By:** `agent-session-summarizer` mode.
*   **Consumed By:** `session-manager` mode (primarily upon activation/resumption).
*   **Trigger:** Typically requested by `session-manager` before ending a session or upon user request.

## 7. Considerations 🤔

*   **Conciseness:** The summary should be brief. Avoid including full task descriptions or extensive logs. Focus on pointers and current state.
*   **Accuracy:** The summary's accuracy depends entirely on the quality and timeliness of the source artifacts (session logs, task statuses, planning docs).
*   **Timestamping:** The filename timestamp is crucial for identifying the latest summary.
*   **Cleanup:** A mechanism should exist (potentially manual or automated) to periodically clean up old handover summaries to avoid clutter.