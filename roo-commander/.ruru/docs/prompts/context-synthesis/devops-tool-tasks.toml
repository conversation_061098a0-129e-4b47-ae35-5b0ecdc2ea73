You are an expert technical writer and AI prompt engineer designing configuration for an automated knowledge base (KB) synthesis system.

Your goal is to generate the content for a TOML file named `devops-tool-tasks.toml`. This file defines specific AI synthesis tasks tailored for DevOps tools and platforms like Docker, Kubernetes, Terraform, Ansible, Grafana, Prometheus, etc. These tools focus on infrastructure management, automation, deployment, monitoring, and related operational concerns.

The output TOML **MUST** strictly adhere to the following structure, defined by the system's `README.md` for task set templates:

--- START OF SCHEMA DEFINITION ---

# TOML definition for synthesis tasks for a specific library type.

# Required: Identifies the type this task set applies to. Matches the key used in library-types.json.
library_type = "example-type" # Replace with the actual type

# Required: An array of task tables. Each table defines one synthesis task.
[[tasks]]
  # Required: Unique identifier for this task within the set. (e.g., "core_concepts", "component_props_summary")
  task_id = "task_identifier_1"

  # Required: Human-readable description of the task's goal.
  description = "Generate an overview of core concepts and principles."

  # Required: List of source KB category directory names to use as input for this task.
  # The synthesizer will read all .md files from these categories within the library's source KB.
  # Common categories include: "guide", "api", "reference", "concepts", "tutorial", "cookbook", "examples", "config", "start", "installation", "misc", "about", "commands", "architecture", "resources", "cli", "yaml", "hcl", "dockerfile" (if specific)
  input_categories = ["guide", "concepts", "about"]

  # Required: The base filename for the synthesized output markdown file.
  # It will be saved in `.ruru/modes/{mode_slug}/kb/{library_name}/synthesized/`.
  output_filename = "core-concepts-summary.md"

  # Required: Specific instructions/prompt focus for the agent-context-synthesizer mode.
  # This tells the AI *what* to focus on when reading the input files for this specific task.
  prompt_focus = "Identify and explain the fundamental ideas, design philosophy, and main features based *only* on the provided input files. Aim for a conceptual overview."

# Add more [[tasks]] tables as needed for this library type.

--- END OF SCHEMA DEFINITION ---

Now, generate the TOML content for `devops-tool-tasks.toml`. Ensure you set `library_type = "devops-tool"`.

Include distinct `[[tasks]]` for the following key aspects commonly found in documentation for DevOps tools:

1.  **Tool Overview & Core Concepts:** What problem does the tool solve? What are its fundamental concepts (e.g., containers, orchestration, Infrastructure as Code (IaC), configuration management, observability, monitoring, alerting)?
2.  **Installation & Initial Setup:** Typical installation methods (package manager, binary download) and basic configuration needed to get started.
3.  **Primary CLI Commands / API Usage:** Summarize the most crucial CLI commands or primary API interactions for core operations (e.g., `docker run/build/push`, `kubectl apply/get/describe`, `terraform init/plan/apply`, `ansible-playbook`, PromQL basics, Grafana API usage). Focus on the *purpose* of key commands.
4.  **Configuration File Formats & Structure:** Describe the primary configuration file syntax and structure (e.g., Dockerfile syntax, Kubernetes YAML manifests structure, Terraform HCL syntax (providers, resources, variables), Ansible Playbook YAML structure, Prometheus `prometheus.yml`, Grafana provisioning).
5.  **Key Resource Types / Objects:** Identify and explain the main resource types or objects managed by the tool (e.g., Docker Images/Containers/Volumes/Networks; K8s Pods/Deployments/Services/Ingresses; Terraform Providers/Resources/Modules; Ansible Plays/Tasks/Roles/Inventories; Grafana Dashboards/Datasources/Alerts; Prometheus Targets/Rules).
6.  **Common Workflows & Use Cases:** Summarize typical sequences of operations or common use cases (e.g., containerizing an application, deploying a service to Kubernetes, provisioning cloud infrastructure, configuring a server fleet, setting up a monitoring dashboard).
7.  **Tool Architecture / Components (If Applicable):** Briefly describe the high-level architecture or key components of the tool (e.g., Docker Engine vs Client; Kube API Server/Controller/Scheduler/Kubelet; Terraform Core vs Providers; Ansible Control Node vs Managed Nodes; Grafana Server/Agent).
8.  **Integration Points & Ecosystem:** How does this tool typically interact with other common tools in the DevOps landscape (e.g., CI/CD systems, cloud providers, other monitoring tools)?

For each task, define a unique `task_id`, a clear `description`, suggest appropriate `input_categories` (consider "concepts", "guide", "cli", "reference", "config", "architecture", "examples", "tutorial", "installation"), choose a descriptive `output_filename` (ending in `.md`), and write a concise but specific `prompt_focus` instructing the synthesizer AI.

Output **ONLY** the raw TOML content suitable for saving directly into the `devops-tool-tasks.toml` file. Do not include any explanatory text before or after the TOML content itself.