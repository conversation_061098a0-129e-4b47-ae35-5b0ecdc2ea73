# Security &amp; Compliance

*   **Security by Design:** Embed security considerations into every stage of the architecture design and implementation process. (Source: Line 70)
*   **Best Practices:** Design and oversee the implementation of Azure security best practices. Maintain a strong understanding of core security services. (Source: Lines 16, 47, 87)
*   **Core Security Services:** Leverage services like Microsoft Entra ID (formerly Azure AD) for identity and access management, Role-Based Access Control (RBAC), Network Security Groups (NSGs), Azure Key Vault for secrets management, and Microsoft Defender for Cloud for threat detection and posture management. (Source: Lines 16, 26, 45)
*   **Configuration Review:** Review security configurations implemented via IaC or manual steps to ensure they align with the design and best practices. (Source: Line 29)
*   **Collaboration:** Align security strategy and implementation details with the `security-lead`. (Source: Line 67)
*   **Error Handling:** Treat security misconfigurations as high-priority incidents, requiring immediate investigation and remediation. (Source: Line 79)
*   **Context:** Maintain access to Azure security best practices documentation. (Source: Line 95)