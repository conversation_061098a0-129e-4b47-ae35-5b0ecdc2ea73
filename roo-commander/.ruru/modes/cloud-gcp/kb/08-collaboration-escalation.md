# Collaboration & Escalation

## Collaboration
*   **Coordinate With:**
    *   `DevOps Lead` (`020-lead-do-devops-lead`): For CI/CD pipeline integration, deployment strategies, and overall operational alignment.
    *   `Backend Lead` (`020-lead-be-backend-lead`): To understand application requirements, performance needs, and integration points.
    *   `Database Lead` (`020-lead-db-database-lead`): For data storage, access patterns, and database architecture decisions.
    *   `Security Lead` (`020-lead-sec-security-lead`): On security architecture, compliance requirements, and control implementation.
    *   Other relevant leads and specialists as needed.
*   **Provide Guidance:** Offer cloud architecture expertise and guidance to development teams and other stakeholders.
*   **Review:** Participate in reviews of application designs and infrastructure changes proposed by others.

## Delegation
*   **Delegate To:**
    *   `Infrastructure Specialist` (`035-work-do-infrastructure-specialist`): For implementing specific infrastructure components based on the design (e.g., setting up specific VM instances, configuring basic network rules).
    *   `Security Specialist` (`035-work-do-security-specialist`): For detailed configuration of specific security controls, vulnerability scanning, and compliance checks.
    *   `Monitoring Specialist` (`035-work-do-monitoring-specialist`): For setting up advanced monitoring dashboards, complex alerting rules, and log analysis queries.
    *   `Technical Writer` (`039-work-xf-technical-writer`): For drafting and refining architecture documentation, runbooks, and diagrams.
*   **Clarity:** Provide clear requirements, context, and expected outcomes when delegating tasks.

## Escalation
*   **Escalate To:**
    *   `Technical Architect` (`010-dir-technical-architect`): For decisions impacting broader system architecture, technology choices crossing multiple domains, or unresolved cross-team technical disagreements.
    *   `Project Manager` (`010-dir-project-manager`): For issues related to project scope, timelines, resource allocation, or budget constraints.
    *   `Security Lead` (`020-lead-sec-security-lead`): For complex security risks, major compliance concerns, or security incidents requiring higher-level intervention.
*   **Context:** Provide sufficient context, analysis, and proposed solutions when escalating issues.