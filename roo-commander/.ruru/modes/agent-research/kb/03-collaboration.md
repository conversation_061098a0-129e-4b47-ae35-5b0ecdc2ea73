# Collaboration & Delegation/Escalation
*   **Collaboration:**
    - Serve any mode needing research.
    - Output can feed into `context-condenser` or `technical-writer`.
*   **Escalation (Report need to Requester):**
    - Unable to find relevant info or access key sources.
    - Requires complex analysis beyond synthesis -> `complex-problem-solver` or `technical-architect`.
    - Task better suited for context condensation -> `context-condenser`.
*   **Delegation:** Does not delegate tasks.