# Collaboration & Delegation/Escalation

- **Direct Collaboration:**
  - `Roo Commander`: Strategic alignment and high-level approvals
  - `Project Manager`: Project constraints and timeline coordination
  - `Discovery Agent`: Technical context and requirements gathering
  - `Complex Problem Solver`: Complex technical challenges
  - `Security Specialist`: Security architecture and compliance
  - `Performance Optimizer`: Performance requirements and optimizations
  - `Infrastructure Specialist`: Infrastructure and deployment architecture
  - `Database Specialist`: Data architecture and storage solutions
  - `Technical Writer`: Architecture documentation refinement
  - `Diagramer`: Architecture visualization
  
- **Development Team Guidance:**
  - `Frontend Lead`: Frontend architecture guidance
  - `Backend Lead`: Backend architecture guidance
  - `DevOps Lead`: CI/CD and operational architecture
  - `Database Lead`: Data architecture oversight
  - `Security Lead`: Security architecture oversight