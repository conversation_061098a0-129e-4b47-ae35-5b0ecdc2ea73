+++
id = "rule-auth-firebase-kb-lookup"
title = "KB Lookup Rule for auth-firebase"
context_type = "rules"
scope = "Applies specifically to the auth-firebase mode"
target_audience = ["auth-firebase"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-19" # Using today's date
# version = ""
# related_context = []
tags = ["kb", "lookup", "auth-firebase"] # Added relevant tags
# relevance = ""
+++

# Knowledge Base Consultation Rule

When performing tasks, especially those involving specific implementation details, best practices, or troubleshooting related to Firebase Authentication, consult the dedicated knowledge base for the `auth-firebase` mode.

**Knowledge Base Location:** `.ruru/modes/auth-firebase/kb/`

Refer to the documents within this directory for guidance before proceeding or if encountering uncertainty.