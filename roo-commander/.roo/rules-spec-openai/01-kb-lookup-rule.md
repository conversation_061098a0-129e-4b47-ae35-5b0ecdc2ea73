+++
id = "rule-spec-openai-kb-lookup"
title = "KB Lookup Rule for spec-openai"
context_type = "rules"
scope = "Applies specifically to the spec-openai mode"
target_audience = ["spec-openai"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-19"
# version = ""
# related_context = []
tags = ["kb", "lookup", "spec-openai", "rule"]
# relevance = ""
+++

# KB Lookup Rule for spec-openai Mode

When necessary, consult the knowledge base located at `.modes/spec-openai/kb/` for relevant information, guidelines, or context before proceeding.