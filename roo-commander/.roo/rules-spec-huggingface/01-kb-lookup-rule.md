+++
id = "rule-spec-huggingface-kb-lookup"
title = "KB Lookup Rule for spec-huggingface"
context_type = "rules"
scope = "Applies specifically to the spec-huggingface mode"
target_audience = ["spec-huggingface"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-19" # Using today's date
# version = ""
# related_context = []
tags = ["kb-lookup", "spec-huggingface", "rules"] # Added relevant tags
# relevance = ""
+++

# Knowledge Base Consultation Rule (spec-huggingface)

When performing tasks, especially those requiring specific domain knowledge, implementation patterns, or best practices related to Hugging Face, **consult the dedicated knowledge base (KB)** located at `.ruru/modes/spec-huggingface/kb/`.

This KB contains curated information, guidelines, and examples relevant to the `spec-huggingface` mode's responsibilities. Prioritize information found in the KB to ensure consistency and adherence to established practices.