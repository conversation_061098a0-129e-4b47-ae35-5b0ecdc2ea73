+++
# --- Basic Metadata ---
id = "RURU-RULE-OS-AWARE-CMDS-V3" # Incremented version
title = "Rule: Generate OS-Aware and Syntactically Correct Commands"
context_type = "rules"
scope = "Command generation for execute_command tool based on detected OS"
target_audience = ["all"] # Apply to all modes using execute_command
granularity = "procedure" # Changed from ruleset to procedure as it defines steps
status = "active"
last_updated = "2025-04-22" # Use current date
tags = ["rules", "shell", "commands", "os-awareness", "powershell", "bash", "execute_command", "windows", "linux", "macos", "syntax", "chaining", "conditional-execution"] # Added tags
template_schema_doc = ".ruru/templates/toml-md/16_ai_rule.README.md"
related_context = [".roo/rules/03-standard-tool-use-xml-syntax.md"]
relevance = "Critical: Prevents command execution errors"
+++

# Mandatory Rule: Generate OS-Aware and Syntactically Correct Commands

**Context:** Commands executed via `execute_command` run within the user's VS Code integrated terminal environment. The underlying operating system significantly impacts required command syntax. Assume the host OS is provided via context (e.g., `environment_details.os` with values like `win32`, `darwin`, `linux`).

**Rule:**

When formulating commands intended for execution via the `execute_command` tool, you **MUST** check the operating system context provided (e.g., `environment_details.os`) and generate commands appropriate for that specific platform's default shell, ensuring correct syntax, especially for command chaining.

**Platform-Specific Syntax & Chaining:**

*   **If OS is `win32` (Windows):**
    *   Target Shell: **PowerShell**.
    *   Examples: `Get-ChildItem` (or `ls`/`dir`), `Copy-Item`, `Move-Item`, `Remove-Item`, `New-Item -ItemType Directory`, `$env:VAR_NAME`, `python -m venv .venv`, `.\.venv\Scripts\activate`.
    *   **Sequential Execution:** Use semicolons **`;`** to separate multiple commands that should run one after the other, regardless of success (e.g., `mkdir temp; cd temp`).
    *   **INVALID OPERATOR:** **NEVER use `&&` for chaining commands.** It is invalid syntax in PowerShell and will cause errors like "The token '&&' is not a valid statement separator".
    *   **Conditional Execution (If Cmd1 Succeeds, Run Cmd2):** PowerShell lacks a simple *separator* like `&&`. To achieve this reliably with `execute_command`:
        1.  Execute the first command in one `execute_command` call.
        2.  **Await the result.** Check the `exit_code`. An exit code of `0` typically indicates success.
        3.  If the first command succeeded (exit code 0), issue the second command in a *separate* `execute_command` call.
        4.  **AVOID** generating complex PowerShell `if ($?) {...}` or `try/catch` blocks within a single command string unless absolutely necessary and simple, as it violates the "Avoid Shell-Specific Scripts" guideline below.
    *   Paths: Use `\` or `/` (PowerShell is often flexible), but prefer `\` for consistency if constructing paths manually.
    *   Quoting: Use single quotes `'...'` for literal strings. Use double quotes `"..."` if variable expansion is needed (less common for simple commands).

*   **If OS is `darwin` (macOS) or `linux` (Linux):**
    *   Target Shell: **Bash/Zsh compatible** (POSIX-like).
    *   Examples: `ls`, `cp`, `mv`, `rm`, `mkdir`, `$VAR_NAME`, `python3 -m venv .venv`, `source .venv/bin/activate`.
    *   **Sequential Execution:** Use semicolons **`;`** to separate commands that should run sequentially, regardless of success (e.g., `mkdir temp; cd temp`).
    *   **Conditional Execution (If Cmd1 Succeeds, Run Cmd2):** **MUST use the double ampersand `&&`** (e.g., `cd my_dir && ls -l`). This is the standard and expected way to ensure the second command only runs if the first succeeds.
    *   **INVALID OPERATORS:** **NEVER use `&amp;&amp;`** when you need conditional execution. **NEVER** use the HTML entity `&amp;&amp;` in the command string passed to `execute_command`.
    *   Paths: **MUST** use forward slashes `/`.
    *   Quoting: Use double quotes `"..."` generally, especially if needing variable expansion (`$VAR`). Use single quotes `'...'` for strict literal strings.

**General Guidelines (Applies to ALL OS):**

*   **Simplicity:** Prefer simple, common commands where possible.
*   **Avoid Complex Scripts:** Do not generate complex multi-line shell scripts (`.ps1`, `.sh`) unless specifically requested and appropriate for the task. Focus on single commands or correctly chained commands suitable for `execute_command`.
*   **Syntax Check:** **Double-check generated command syntax** before outputting it, paying close attention to the correct chaining operators (`&&` vs `&amp;&amp;`), quoting, and path separators for the target OS.
*   **User Overrides:** If the user explicitly requests a command for a *different* shell (e.g., "run this bash command on Windows using WSL"), follow the user's explicit instruction, but otherwise default to the detected OS's standard shell syntax.

**Failure to generate OS-appropriate and syntactically correct commands, especially regarding chaining (`&&` vs `&amp;&amp;`), will likely result in execution errors for the user.** Always check the OS context and verify command syntax before generating commands.