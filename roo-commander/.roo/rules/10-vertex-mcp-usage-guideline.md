+++
# --- Basic Metadata ---
id = "RULE-VERTEX-MCP-USAGE-V2" # Incremented version
title = "Guideline: Vertex AI MCP Tool Usage (V2)" # Updated title
context_type = "rules"
scope = "Standardizing the use of Vertex AI MCP tools, especially save vs. direct output, and session-aware storage" # Updated scope
target_audience = ["all"] # Primarily modes using research/generation
granularity = "guideline"
status = "active"
last_updated = "2025-05-07" # Use current date
tags = ["rules", "mcp", "vertex-ai", "guideline", "output-handling", "file-organization", "research", "validation"]
related_context = [
    ".roo/rules/01-standard-toml-md-format.md",
    ".roo/rules/11-session-management.md", # Logging guidance via Session Management
    "repomix-output-shariqriazz-vertex-ai-mcp-server.md" # Reference to the MCP definition
    ]
template_schema_doc = ".ruru/templates/toml-md/16_ai_rule.README.md"
relevance = "High: Ensures consistent handling of MCP outputs"
+++

# Guideline: Vertex AI MCP Tool Usage

**Objective:** To standardize how modes interact with the `vertex-ai-mcp-server`, particularly regarding output handling and the use of research/validation capabilities.

**Applies To:** All modes utilizing tools provided by the `vertex-ai-mcp-server`.

**1. Tool Availability:**

*   Modes receive the list of available MCP tools dynamically in their context. Use these tools only if the `vertex-ai-mcp-server` is connected and the specific tool is listed.
*   Modes should gracefully handle scenarios where the MCP server or specific tools are unavailable.

**2. Output Handling Strategy (General):**

*   **Default to `save_*`:** When using Vertex AI MCP tools that have a corresponding `save_*` variant (e.g., `save_generate_project_guidelines`, `save_doc_snippet`, `save_topic_explanation`, `save_answer_query_direct`, `save_answer_query_websearch`), **prefer using the `save_*` variant** for potentially long or complex outputs.
    *   **Rationale:** This prevents large outputs from cluttering the chat history, archives the result for later reference, and helps manage context window size.
*   **Use Direct Tools Sparingly:** Use the direct output tools (e.g., `answer_query_websearch`, `explain_topic_with_docs`, `get_doc_snippets`) only when the expected output is known to be concise (e.g., a single code snippet, a short definition, a simple yes/no answer) or when specifically requested by the user/coordinator for an inline response.
*   **Reporting Saved Files:** When using a `save_*` tool, the mode **MUST** report the full path to the saved file back to the coordinator/user via `attempt_completion`. (See Section 2.1 for handling persistent context outputs.)

**2.1. Handling Persistent Context Outputs:**

*   **Identify Need:** If the output generated by a `save_*` tool is intended to be *persistent context* referenced directly by mode rules, KBs, or long-term documentation (unlike temporary analysis results):
    1.  **Move & Rename:** After the `save_*` tool successfully creates the file in the default `.ruru/docs/vertex/...` location, the coordinating mode **MUST** immediately use a file system tool (`execute_command` with `mkdir -p` and `mv`, or the `vertex-ai-mcp-server`'s `create_directory` and `move_file_or_directory` tools) to:
        *   Ensure a `context/` subdirectory exists within the relevant mode's directory (e.g., `.ruru/modes/[mode-slug]/context/`).
        *   Move the generated file from `.ruru/docs/vertex/...` into this mode-specific `context/` directory.
        *   Rename the file to something meaningful and versioned if appropriate (e.g., `[mode-slug]_context_[topic]_v1.md`) instead of the default timestamped name.
    2.  **Update References:** Any rules, KBs, or documents referencing this context **MUST** be updated immediately to point to the new, persistent path within the mode's `context/` directory.
    3.  **Log Move:** Log the move and rename operation.
*   **Rationale:** This ensures that context critical to a mode's operation is stored logically with the mode and avoids broken references if the general `.ruru/docs/vertex/` directory is treated as temporary. The default timestamped location should be used for transient outputs or initial review before deciding on persistence.

**3. Standard Output Location & Naming:**

*   **Output Path Construction:** The calling mode is responsible for constructing the `output_path` parameter for the `save_*` tools.
*   **Base Directory Determination:**
    *   **If an active `RooComSessionID` is available to the calling mode:** The base directory for temporary outputs **MUST** be `.ruru/sessions/[RooComSessionID]/artifacts/docs/vertex/`. The `[RooComSessionID]` should be the ID of the currently active session.
    *   **If no active `RooComSessionID` is available:** The base directory **MUST** be `.ruru/docs/vertex/`.
*   **Subdirectory Structure (under the determined Base Directory):** Organize files by the *type* of tool used:
    *   `save_generate_project_guidelines` -> `[Base Directory]/guidelines/`
    *   `save_doc_snippet` -> `[Base Directory]/snippets/`
    *   `save_topic_explanation` -> `[Base Directory]/explanations/`
    *   `save_answer_query_direct` -> `[Base Directory]/answers-direct/`
    *   `save_answer_query_websearch` -> `[Base Directory]/answers-web/`
*   **File Naming Convention:** Files **MUST** be named using the format `[YYYYMMDDHHMMSS]-[sanitized_topic_or_query].md`.
    *   `[YYYYMMDDHHMMSS]`: Timestamp generated by the mode at the time of the call.
    *   `[sanitized_topic_or_query]`: A filesystem-safe representation of the primary topic or query (e.g., replace spaces/special characters with `_`, limit length to ~50 characters).
    *   *Example (within a session):* `.ruru/sessions/SESSION-XYZ-2505072130/artifacts/docs/vertex/guidelines/20250507213500-react_ts_node_guidelines.md`
    *   *Example (no session):* `.ruru/docs/vertex/guidelines/20250507213500-react_ts_node_guidelines.md`
*   **Rationale for Session-Specific Path:** Storing temporary Vertex AI outputs within the active session's artifacts directory keeps session-related files organized and clearly associated with the interaction that generated them. This is particularly useful for temporary analysis or drafts before deciding if an output should become persistent context (see Section 2.1).

**4. Using MCP for Research & Validation ("Brains Trust"):**

*   **Targeted Use:** While tools like `answer_query_websearch` can provide external context or validation, their use should be targeted and judicious due to latency, cost, and potential complexity.
*   **Prioritize Internal Context:** Modes **MUST** prioritize using internal project knowledge (KBs, rules, existing code, task context) before resorting to external MCP calls for validation or general research.
*   **Recommended Modes:** Consider enabling or encouraging this capability primarily within modes focused on architecture, research, complex problem-solving, or senior-level review (e.g., `technical-architect`, `agent-research`, `complex-problem-solver`, `util-senior-dev`, `util-second-opinion`).
*   **Clear Triggers:** Modes utilizing external validation should have clear triggers defined in their specific rules/KBs (e.g., "when evaluating unfamiliar technology", "if confidence is low").
*   **Log Usage:** Log instances where external validation was sought via MCP, including the query and a summary of the outcome, according to standard logging procedures (Rule `08-logging-procedure-simplified.md`).

**Adherence to these guidelines ensures consistent, traceable, and efficient use of the Vertex AI MCP capabilities.**