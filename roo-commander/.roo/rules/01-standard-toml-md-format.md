# Standard: TOML+Markdown (TOML MD) Format Usage

**Applies To:** All agents creating or modifying Markdown files intended to have structured metadata.

**1. General Principle:**

This workspace utilizes the **TOML+Markdown (TOML MD)** format for various documents (tasks, ADRs, documentation, context sources, etc.) to combine machine-readable metadata with human-readable content.
*   **Metadata:** Use **TOML** syntax for key-value pairs. Adhere strictly to valid TOML syntax.
*   **Content:** Use standard **Markdown** for the main body of the document following the metadata.
*   **Rationale:** See `.ruru/templates/toml-md/README.md` for the benefits and general usage guidelines.

**2. Delimiter Requirement (Universal):**

*   **ALL** Markdown files using TOML frontmatter for structured metadata **MUST** enclose the TOML block within triple-plus delimiters (`+++`).

    ```markdown
    +++
    # TOML frontmatter content goes here
    key = "value"
    # ... other fields specific to document type ...
    +++

    # Markdown content follows
    ...
    ```
*   This ensures unambiguous and reliable parsing by automated tools across all document types.

**3. Using Templates:**

*   **When creating a new Markdown document** that requires structured metadata (e.g., tasks, ADRs, documentation, context sources), **ALWAYS** use the TOML+MD format described above.
*   **Check for Existing Templates:** Before creating a new document from scratch, consult the **`.ruru/templates/toml-md/README.md`** file to see if a suitable template already exists in the `.ruru/templates/toml-md/` directory.
    *   Do **NOT** read all files in the `.ruru/templates/toml-md/` directory directly; rely on the README for the list of available templates and their descriptions.
    *   If a suitable template exists (e.g., `01_mdtm_feature.md`, `07_adr.md`), use it as the starting point and adhere to the TOML schema defined within that template.
*   **Creating New Templates:**
    *   If no specific template exists in `.ruru/templates/toml-md/` for the document type you need to create:
        1.  **Offer to Create:** Propose creating a new template with the user.
        2.  **Start with Boilerplate:** If the user agrees, copy the boilerplate template (`.ruru/templates/toml-md/00_boilerplate.md`) as a starting point.
        3.  **Define Schema:** Define the necessary TOML metadata fields within the `+++` delimiters, clearly commenting on their purpose and whether they are required.
        4.  **Structure Content:** Structure the Markdown body logically using appropriate headings.
        5.  **Save Template:** Save the new template file within the `.ruru/templates/toml-md/` directory, following the naming convention (e.g., `NN_description.md`).
        6.  **Update README:** **Crucially**, update the `.ruru/templates/toml-md/README.md` file to include the newly created template in the "Available Templates" list with a brief description. This makes it discoverable for future use.

**4. Key Considerations:**

*   **Syntax:** Always use valid TOML syntax within the `+++` block.
*   **Schema:** Adhere to the specific schema defined for the document type (either from an existing template or the newly defined one).
*   **Consistency:** Use defined field names and data types consistently.

**Failure to use the correct format (including `+++` delimiters) will cause errors in processing and build steps.**