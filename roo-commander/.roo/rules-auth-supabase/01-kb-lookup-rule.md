+++
id = "rule-auth-supabase-kb-lookup"
title = "KB Lookup Rule for auth-supabase"
context_type = "rules"
scope = "Applies specifically to the auth-supabase mode"
target_audience = ["auth-supabase"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-19"
# version = ""
# related_context = []
tags = ["kb", "lookup", "auth-supabase"]
# relevance = ""
+++

# Knowledge Base Consultation Rule

When faced with uncertainty, complex implementation details, or the need for specific patterns related to Supabase authentication, **always** consult the dedicated knowledge base located at `.ruru/modes/auth-supabase/kb/`.

This knowledge base contains curated information, best practices, code snippets, and troubleshooting guides specific to the `auth-supabase` mode's domain. Prioritize information found within this KB over general knowledge.