# Meet the Roo AI Team

Welcome to your AI development team! Each team member (mode) has specialized skills to help with different aspects of your project.

## 🪃 Orchestrator - The Project Manager

**Personality:** Methodical, organized, and directive
**Specialties:** Task planning, delegation, and coordination
**About:** Orchestrator excels at breaking down complex projects into manageable tasks and assigning them to the right team members. They never do the actual implementation work themselves but ensure everything runs smoothly and all pieces fit together perfectly.

**When to use:** When you need to plan a complex project, coordinate multiple specialists, or ensure consistent documentation across a large codebase.

## 💻 Code - The Developer

**Personality:** Pragmatic, precise, and methodical
**Specialties:** Software implementation, testing, and optimization
**About:** Code is your go-to developer who writes clean, efficient, and maintainable code across multiple programming languages. They approach problems by breaking them down into logical components and building solutions incrementally.

**When to use:** When you need to implement features, write algorithms, optimize performance, or refactor existing code.

## 🏛️ Architect - The System Designer

**Personality:** Visionary, systematic, and detail-oriented
**Specialties:** System design, pattern application, and technical strategy
**About:** Architect specializes in designing complex systems with a focus on scalability, maintainability, and performance. They excel at creating clear documentation and visual representations of system components and their interactions.

**When to use:** When you need to design a new system, plan a major refactoring, or document architectural decisions.

## ❓ Ask - The Information Specialist

**Personality:** Curious, thorough, and balanced
**Specialties:** Information gathering, source evaluation, and knowledge synthesis
**About:** Ask is an expert at finding and evaluating information from diverse sources. They maintain rigorous citation practices and can synthesize complex information into clear, concise summaries.

**When to use:** When you need factual information, explanations of concepts, or summaries of complex topics.

## 🪲 Debug - The Problem Solver

**Personality:** Analytical, persistent, and methodical
**Specialties:** Error analysis, root cause identification, and solution validation
**About:** Debug is a master troubleshooter who can trace execution flows, identify failure points, and distinguish symptoms from underlying issues. They use a systematic approach combining hypothesis formation, targeted testing, and evidence collection.

**When to use:** When you encounter errors, performance issues, or unexpected behaviors in your code or systems.

## 💾 Memory - The Knowledge Manager

**Personality:** Organized, meticulous, and systematic
**Specialties:** Information organization, metadata engineering, and knowledge lifecycle management
**About:** Memory specializes in structuring, categorizing, and indexing knowledge for optimal retrieval and usage. They maintain systematic organization of diverse information types and create rich, interconnected knowledge webs.

**When to use:** When you need to organize project documentation, create knowledge bases, or establish information management systems.

## 🔍 Deep Research - The Investigator

**Personality:** Thorough, analytical, and insightful
**Specialties:** Comprehensive research, analytical framework application, and knowledge integration
**About:** Deep Research conducts structured, multi-phase research that progresses from broad exploration to focused investigation. They excel at identifying patterns, establishing relationships, and uncovering hidden insights.

**When to use:** When you need in-depth research on complex topics, competitive analysis, or comprehensive understanding of emerging technologies.

---

## How the Team Works Together

The Roo AI team follows the "Boomerang Pattern" for seamless collaboration:

1. **Orchestrator** breaks down your request into specific tasks
2. **Orchestrator** assigns each task to the appropriate specialist
3. **Specialists** complete their assigned tasks
4. **Specialists** return their work to Orchestrator
5. **Orchestrator** integrates all the pieces and delivers the final result

This structured workflow ensures efficient use of resources, clear accountability, and high-quality deliverables for all your projects.