# Perplexity Search: Information Design Principles for Error Reporting Clarity

## Prompt
```
information design principles for error reporting clarity
```

## Response
To ensure clarity in error reporting, follow these information design principles for error messages:

1.  **Clear and Unambiguous Messages**:
    *   **Clarity**: Use simple language that is easy to understand.
    *   **Context**: Tailor the message to fit the context of use.
    *   **Purpose**: Clearly describe what happened, why it happened, and how to resolve the issue[2].

2.  **High Visibility and Accessibility**:
    *   **Proximity**: Display error messages close to where the error occurred.
    *   **Noticeable Indicators**: Use bold, high-contrast visuals like red text or highlights. Ensure accessibility by not relying solely on color or animation[1].

3.  **Design Based on Impact**:
    *   **Severity Indication**: Differentiate between minor and severe errors.
    *   **Communication Methods**: Use modal dialogs for critical issues and toast notifications for less severe ones[1].

4.  **Preventive Design**:
    *   **Error Prevention**: Design the interface to minimize errors from occurring in the first place[4].

5.  **Timing and Display**:
    *   **Avoid Premature Errors**: Only display errors when necessary and not prematurely[1].