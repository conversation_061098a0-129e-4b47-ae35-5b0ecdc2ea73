---
title: Research Log - Task 3.2: Error Pattern Feedback Loop
task_id: task_3.2
date: 2025-05-19
last_updated: 2025-05-19
status: IN_PROGRESS
owner: deep-research-agent
---

# Research Log: Task 3.2 - Error Pattern Feedback Loop

## Objective
Design a system to feed detected error patterns back into Roo Code's mode system prompts as examples to dynamically enhance prompt effectiveness and reduce errors.

## Phase 1: Initial Research & Analysis (System Prompt Management)

### 1.1. Consultation of Memory System (User Directed)
*   **Date:** 2025-05-19
*   **Action:** Queried `memory-agent-sql` for existing information.
*   **Queries & Prompts Used:**
    *   `search_memories` with query: "dynamic system prompt enhancement"
    *   `search_memories` with query: "LLM error feedback loops"
    *   `search_memories` with query: "few-shot learning from errors"
    *   `search_memories` with query: "Roo Code prompt management"
*   **Responses/Findings:**
    *   All queries returned empty results. No pre-existing information found in the memory system for these topics.
*   **Reflection:** The absence of information in the memory system means this research will establish the initial knowledge base for these concepts within the project.

### 1.2. Review Task 0.2 Output: `mode_configuration_architecture_analysis.md`
*   **Date:** 2025-05-19
*   **Action:** Read and analyzed `research/synthesis/mode_configuration_architecture_analysis.md`.
*   **Key Findings:**
    *   System prompts are generated by a central `SYSTEM_PROMPT` function.
    *   **File-Based Overrides:** Prompts can be overridden by files at `.roo/system-prompt-${mode_slug}`. This is a strong candidate for injecting error examples.
    *   **`customInstructions`:** Mode configurations (`ModeConfig`) have a `customInstructions` field, another viable injection point, managed by `CustomModesManager`.
    *   `CustomModesManager` caches mode configurations for 10 seconds and has an `updateCustomMode` method.
*   **Relevance to Task 3.2:** The file-based override mechanism and `customInstructions` (potentially updated via `updateCustomMode`) are the most promising ways to dynamically update system prompts with error examples.

### 1.3. Exploration of Roo Code Structure for Prompt Management Files
*   **Date:** 2025-05-19
*   **Action:** Listed files in `Roo-Code/` and `Roo-Code/src/shared/`.
*   **Findings:** Confirmed `modes.ts` location at `Roo-Code/src/shared/modes.ts`.
*   **Action:** Searched for `getSystemPromptFilePath` in `Roo-Code/src/`.
*   **Findings:**
    *   Definition found in `Roo-Code/src/core/prompts/sections/custom-system-prompt.ts`.
    *   Path constructed as: `path.join(cwd, ".roo", \`system-prompt-${mode_slug}\`)`. This confirms the location and naming convention for override files.
*   **Action:** Searched for `CustomModesManager` in `Roo-Code/src/`.
*   **Findings:**
    *   Definition found in `Roo-Code/src/core/config/CustomModesManager.ts`.
    *   Confirmed 10-second cache TTL (`cacheTTL = 10_000`).
    *   Confirmed existence of `updateCustomMode` method.
    *   `CustomModesManager` is instantiated in `ClineProvider` with a callback to `postStateToWebview`, suggesting UI updates upon mode changes.

### 1.4. Summary of Prompt Management Analysis for Dynamic Updates
*   **Preferred Method:** Modifying `.roo/system-prompt-${mode_slug}` files due to direct impact and likely bypassing the 10s mode config cache.
*   **Alternative Method:** Modifying `customInstructions` in `.roomodes` files, potentially using `CustomModesManager.updateCustomMode` for faster updates.

## Phase 2: Design of Error Feedback Loop Architecture

### 2.1. Proposed Architecture Diagram & Components
*   **Date:** 2025-05-19
*   **Action:** Designed a conceptual architecture for the error feedback loop.
*   **Diagram (Mermaid):**
    ```mermaid
    graph TD
        subgraph "Error Sources"
            A1[Semantic Guardrails]
            A2[Consistency Checks]
            A3[User Corrections/Feedback]
            A4[Other Monitoring (e.g., Phase 1 & 2 outputs)]
        end

        A1 --> B[Error Collector Service]
        A2 --> B
        A3 --> B
        A4 --> B

        subgraph "Error Processing & Storage"
            B --> C{Error Formatter & Normalizer}
            C --> D[Error Database/Store]
            D --> E{Error Prioritizer & Selector}
            E --> F[Generalized Error Pattern Extractor (Optional)]
        end

        subgraph "Prompt Update Mechanism"
            F --> G[Prompt Enhancer Module]
            E --> G
            G --> H{Targeted Prompt File Updater}
            H --> I1[".roo/system-prompt-modeA.md"]
            H --> I2[".roo/system-prompt-modeB.md"]
            H --> J[CustomModesManager (.roomodes updater - Alternative)]
        end

        subgraph "Roo Code Core"
            K[SYSTEM_PROMPT Function] --> L[LLM]
            I1 --> K
            I2 --> K
            J -- Loads --> K
        end

        subgraph "Evaluation & Monitoring"
            L -- Output --> M[Error Detection (again)]
            M --> N[Performance Metrics Tracker]
            N --> O[Dashboard/Reporting]
            D -- Data for --> N
        end
    ```
*   **Component Descriptions:**
    1.  **Error Sources:** Semantic guardrails, consistency checks, user corrections, other monitoring.
    2.  **Error Collector Service:** Centralized reception and initial standardization of error reports.
    3.  **Error Formatter & Normalizer:** Structures and normalizes error data.
    4.  **Error Database/Store:** Persists formatted errors.
    5.  **Error Prioritizer & Selector:** Selects valuable errors based on frequency, severity, recency, etc. Manages example count.
    6.  **Generalized Error Pattern Extractor:** (Optional) Generalizes specific errors into broader patterns.
    7.  **Prompt Enhancer Module:** Formats error patterns for prompt injection.
    8.  **Targeted Prompt File Updater:** Modifies `.roo/system-prompt-${mode_slug}` files or `.roomodes` via `CustomModesManager`.
    9.  **Roo Code Core:** Existing components utilizing enhanced prompts.
    10. **Performance Metrics Tracker & Reporting:** Monitors loop effectiveness.

### 2.2. Considerations for Error Processing and Integration
*   **(Ongoing) Error Collection:** How to interface with diverse error sources.
*   **(Ongoing) Error Formatting:** Defining a robust schema for error instances.
*   **(Ongoing) Prioritization & Selection:**
    *   **Frequency of Updates:** Batched (e.g., daily/hourly) vs. near real-time.
    *   **Number of Examples:** E.g., top 3-5, rotating set, considering prompt length limits.
    *   **Algorithm:** Weighted scoring (frequency, severity, recency, user flags, diversity).
*   **(Ongoing) Generalization:** LLM-based vs. rule-based generalization.
*   **(Ongoing) Integration into Prompts:** Primarily via `.roo/system-prompt-${mode_slug}` files.

## Phase 3: Prompt Enhancement Strategy & Evaluation (Ongoing)

### 3.1. Prompt Enhancement Strategy
*   *(To be detailed: How error examples are presented in prompts)*
    *   Example: "AVOID: [Erroneous output] (Reason: [short reason]). PREFER: [Corrected output/pattern]."

### 3.2. Integration Plan
*   *(To be detailed: How the new feedback loop system interfaces with existing Roo Code components)*

### 3.3. Evaluation Metrics
*   *(To be detailed: Metrics for assessing effectiveness)*
    *   Error rate reduction (overall, per mode, per error type).
    *   Frequency of specific error patterns.
    *   User satisfaction / reduction in manual corrections.

### 3.4. Examples of Enhanced Prompts
*   *(To be developed)*

---
*This log will be updated as the task progresses.*