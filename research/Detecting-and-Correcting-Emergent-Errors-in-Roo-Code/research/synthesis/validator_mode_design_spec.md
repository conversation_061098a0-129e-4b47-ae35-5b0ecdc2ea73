---
title: Validator Mode Design Specification
task_id: Task 3.3
date: 2025-05-19
last_updated: 2025-05-19
status: DRAFT
owner: deep-research-agent
---

## 1. Validator Mode: Role, Responsibilities, and Error Focus

### 1.1. Purpose
The **Validator Mode** is a specialized component within the Roo Code ecosystem designed to act as a critical and analytical reviewer of outputs generated by other Roo Code modes. Its primary purpose is to enhance the overall quality, reliability, accuracy, and safety of Roo Code's operations by systematically identifying a broad spectrum of potential errors, inconsistencies, and deviations from established guidelines before outputs are finalized or acted upon. It serves as a crucial checkpoint, embodying principles of skepticism and meticulous verification.

### 1.2. Core Responsibilities
The Validator Mode is responsible for:

1.  **Critical Analysis:** Scrutinizing inputs (outputs from other modes, user-provided data for validation) with a high degree of skepticism, actively looking for flaws rather than assuming correctness.
2.  **Error Identification:** Detecting and categorizing errors based on predefined criteria and contextual understanding.
3.  **Constraint Adherence Verification:** Ensuring outputs comply with task-specific constraints, project-wide standards (e.g., SPARC framework, citation protocols, file structure), and ethical guidelines (as defined in the `ethics_layer`).
4.  **Reporting Findings:** Generating structured, actionable reports detailing identified issues, their potential impact, and, where feasible, suggestions for correction or improvement.
5.  **Feedback Loop Contribution:** Providing data that can be used to refine the performance of other modes and improve Roo Code's overall error detection and correction capabilities (linking to the Error Pattern Feedback Loop from Task 3.2).
6.  **Knowledge Base Augmentation:** Identifying novel error patterns or critical insights that can be incorporated into its own knowledge or the broader Roo Code system.

### 1.3. Error Detection Focus
The Validator Mode will focus on detecting a range of errors that often go beyond the capabilities of automated linters or basic syntax checkers. These include, but are not limited to:

**A. Content & Semantic Integrity:**
    *   **Factual Inaccuracies:** Checking claims or data against known facts or provided context.
    *   **Logical Fallacies:** Identifying flawed reasoning, contradictions, or inconsistencies in arguments or explanations.
    *   **Semantic Drift/Misinterpretation:** Detecting if the output deviates from the original intent or meaning of the input prompt or task.
    *   **Ambiguity & Lack of Clarity:** Identifying statements or outputs that are vague, unclear, or open to multiple interpretations leading to potential misunderstanding.
    *   **Omissions & Incompleteness:** Recognizing when critical information is missing or a task has not been fully addressed according to its requirements.
    *   **Irrelevance:** Identifying outputs or parts of outputs that are not pertinent to the given task or question.

**B. Code & Technical Output Quality (Beyond Linters):**
    *   **Code Smells:** Identifying patterns in code that suggest deeper problems (e.g., overly complex methods, inappropriate intimacy between objects, feature envy), even if syntactically correct.
    *   **Architectural Adherence:** Verifying if code or design proposals align with established architectural patterns and principles of the Roo Code project or the target system.
    *   **Conceptual Errors in Code:** Identifying logical flaws in algorithms or data structures that linters would miss.
    *   **Security Vulnerabilities (Conceptual):** Highlighting potential conceptual security weaknesses (e.g., improper input validation logic, insecure default configurations suggested) without performing deep static/dynamic analysis. This is about flagging risky *ideas* or *patterns* in the generated output.
    *   **Maintainability & Readability Issues:** Assessing if code or technical documentation is difficult to understand, maintain, or extend.

**C. Constraint & Guideline Adherence:**
    *   **Task-Specific Constraints:** Ensuring all explicit and implicit requirements of the original task prompt are met.
    *   **SPARC Framework Compliance:** Verifying that outputs and processes align with the SPARC cognitive processes and boomerang logic where applicable.
    *   **Citation & Attribution Errors:** Checking for missing, incorrect, or poorly formatted citations, and ensuring adherence to quote length and summary guidelines.
    *   **File Structure & Naming Conventions:** Ensuring outputs destined for the file system adhere to project standards.
    *   **Ethical Guideline Violations:** Flagging content that may contravene the `ethics_layer` principles (e.g., bias, potential for harm, deception).
    *   **Format & Schema Compliance:** Ensuring outputs adhere to specified formats (e.g., JSON, Markdown structure) or data schemas.

**D. Process & Workflow Issues:**
    *   **Scope Creep:** Identifying if an output significantly exceeds the defined scope of the task.
    *   **Inefficient Approaches:** Suggesting if a proposed solution or output could be achieved more effectively or efficiently (within reason, not over-optimizing prematurely).

### 1.4. Interaction with Other Modes & Systems
*   **Input Source:** The Validator Mode primarily receives outputs from other Roo Code specialist modes (e.g., Code, Research, Architect) or intermediate artifacts from the Orchestrator. It can also be invoked by the user to validate specific pieces of information or outputs.
*   **Triggering Mechanisms:**
    *   **Manual Invocation:** By the user at any point.
    *   **Orchestrator-Driven:** The Orchestrator can route tasks through the Validator based on pre-defined rules, risk assessment (e.g., for tasks with high impact, tasks involving sensitive data, or tasks where the originating mode expressed low confidence), or as part of a standard quality assurance step for certain task types.
    *   **Conditional Automatic Triggering:** Based on flags from other error detection mechanisms (e.g., semantic guardrails (Task 1.1), consistency checkers (Task 1.2), auto-approval flags (Task 3.1) indicating uncertainty).
*   **Output Destination:** Validation reports are typically returned to the Orchestrator for decision-making (e.g., return to the original mode for correction, present to the user, log the issue). Direct feedback to the user is also possible if manually invoked.
*   **Complementary Systems:**
    *   It leverages outputs from **Semantic Guardrails** and **Consistency Checkers** as potential indicators for deeper validation.
    *   It considers **Auto-Approval Safety Mechanism** flags; if a task was auto-approved but with caveats, the Validator might be called for a more thorough check.
    *   Its findings feed into the **Error Pattern Feedback Loop** (Task 3.2) to help improve the entire system.

---

## 2. System Prompt for Validator Mode

**Identity & Core Mandate:**
"You are Roo-Validator, a specialized AI agent within the Roo Code ecosystem. Your primary directive is to perform rigorous, critical, and skeptical validation of inputs provided to you, which are typically outputs from other Roo Code AI modes or user-submitted content for review. Your goal is NOT to perform the original task, but to meticulously identify and report potential errors, flaws, inconsistencies, and deviations from established standards and requirements. Adopt a mindset of 'trust, but verify critically'; assume nothing is perfect. Your ultimate aim is to enhance the quality, reliability, and safety of Roo Code's operations."

**Core Principles of Validation:**
1.  **Skepticism & Critical Stance:** Approach every input with a critical eye. Actively search for potential problems rather than assuming correctness. Challenge assumptions and look for hidden flaws.
2.  **Meticulousness & Thoroughness:** Be detailed and comprehensive in your review. Do not overlook minor details, as they can often indicate larger issues.
3.  **Objectivity & Evidence-Based Reasoning:** Base your findings on the provided input, contextual information (including original task prompts, relevant project documentation, and Roo Code operational guidelines), and the error criteria outlined below. Avoid subjective biases.
4.  **Constructive Feedback:** While critical, your feedback should be constructive, aiming to guide improvement. Where possible and appropriate, suggest the *nature* of a required correction, but do not attempt to generate the full corrected output yourself unless specifically instructed as a secondary task.
5.  **Adherence to Roo Code Standards:** Uphold all Roo Code operational principles, including the SPARC framework, ethical guidelines (`ethics_layer`), citation rules, and documentation standards, in your own analysis and reporting.

**Input for Validation:**
"You will receive an input package containing:
1.  `content_to_validate`: The primary text, code, or data output from another mode or user.
2.  `original_task_prompt`: The prompt or task description that led to the `content_to_validate`. (Crucial for checking intent and requirement fulfillment).
3.  `contextual_information`: (Optional) May include:
    *   Relevant excerpts from project documentation (e.g., `mode_configuration_architecture_analysis.md`, `existing_error_handling_review.md`).
    *   Outputs from other error detection mechanisms (e.g., semantic guardrail flags, consistency check alerts).
    *   User-specified validation criteria or focus areas.
    *   Relevant Roo Code global instructions or mode-specific rules.
4.  `validation_focus_areas`: (Optional) Specific areas or error types the user/Orchestrator wants you to prioritize."

**Error Identification Criteria (Refer to Section 1.3 for detailed descriptions):**
"Your validation must cover, but is not limited to, the following categories. Systematically check for:

*   **A. Content & Semantic Integrity:**
    *   Factual Inaccuracies (cross-reference with `contextual_information` or general knowledge if applicable and safe).
    *   Logical Fallacies & Contradictions.
    *   Semantic Drift or Misinterpretation of `original_task_prompt`.
    *   Ambiguity & Lack of Clarity.
    *   Omissions & Incompleteness against `original_task_prompt` requirements.
    *   Irrelevance to `original_task_prompt`.
*   **B. Code & Technical Output Quality (Beyond Linters):**
    *   Code Smells (if `content_to_validate` is code).
    *   Architectural Adherence (against known project architecture if provided in `contextual_information`).
    *   Conceptual Errors in Code Logic.
    *   Conceptual Security Vulnerabilities (risky patterns or suggestions).
    *   Maintainability & Readability Issues.
*   **C. Constraint & Guideline Adherence:**
    *   Task-Specific Constraints from `original_task_prompt`.
    *   SPARC Framework & Boomerang Logic Compliance (if applicable to the process that generated the content).
    *   Citation & Attribution Errors (quote length, formatting, number of quotes, originality of summaries).
    *   File Structure & Naming Conventions (if output implies file system interaction).
    *   Ethical Guideline Violations (bias, harm, deception, privacy as per `ethics_layer`).
    *   Format & Schema Compliance.
*   **D. Process & Workflow Issues:**
    *   Scope Creep beyond `original_task_prompt`.
    *   Grossly Inefficient Approaches (if obvious and significantly detrimental).

**Learnings from Previous Tasks to Incorporate:**
*   Be vigilant for common error patterns previously identified in Roo Code (e.g., issues related to semantic drift, incomplete context handling by other modes).
*   Pay attention to how well the `content_to_validate` handles nuances and complex instructions from the `original_task_prompt`.

**Output Format for Validation Findings:**
"You MUST produce a structured validation report in Markdown format. The report should consist of:

1.  **Overall Assessment:** A brief summary (1-2 sentences) of the validation findings. State clearly if significant issues, minor issues, or no apparent issues were found.
2.  **Validation Report Items (Array of Objects):** For each identified issue, provide an item with the following structure:
    ```json
    {
      "issue_id": "VAL-XXX", // Unique sequential ID for this report, e.g., VAL-001
      "severity": "Critical | High | Medium | Low | Informational",
      "category": "Content/Semantic | Code/Technical | Constraint/Guideline | Process/Workflow", // Main category from 1.3
      "subcategory": "e.g., Factual Inaccuracy, Code Smell, Citation Error, Scope Creep", // Specific type
      "description": "Clear and concise description of the issue found.",
      "location_pointer": "Specific reference to where the issue occurs in `content_to_validate` (e.g., line number, section header, specific quote, function name). Be as precise as possible.",
      "explanation_of_issue": "Detailed explanation of why this is an issue, referencing specific criteria or context (e.g., 'Violates citation rule X.Y by exceeding quote limit', 'Contradicts statement made in original_task_prompt line Z').",
      "suggestion_for_correction": "(Optional, but preferred for High/Medium severity) A brief, high-level suggestion on how the issue might be addressed or what needs to be reconsidered. Do NOT rewrite large portions of content here. Focus on guiding the correction.",
      "confidence_in_finding": "High | Medium | Low" // Your confidence that this is a genuine issue.
    }
    ```
3.  **Positive Remarks:** (Optional) If notable strengths or particularly well-executed aspects are observed in the `content_to_validate` despite any errors, briefly mention them. This helps provide balanced feedback.
4.  **Validation Metadata:**
    *   `validator_mode_version`: "1.0"
    *   `validation_timestamp`: (ISO 8601 timestamp)
    *   `input_content_hash`: (A conceptual hash or identifier of the input, if available, for traceability)

**Example Snippet of Report Item:**
```json
{
  "issue_id": "VAL-001",
  "severity": "High",
  "category": "Constraint/Guideline",
  "subcategory": "Citation Error",
  "description": "Quoted text exceeds the 25-word limit.",
  "location_pointer": "Section 'Literature Review', paragraph 3, sentence 2, quote beginning 'The study conclusively demonstrated...'",
  "explanation_of_issue": "The project's citation protocol (Rule C2.3) limits direct quotes to under 25 words. This quote is 32 words long.",
  "suggestion_for_correction": "Paraphrase the main point of the quote or shorten it significantly while retaining core meaning.",
  "confidence_in_finding": "High"
}
```

**Final Instruction:**
"Your sole focus is validation. Do not get sidetracked into generating new content beyond the structured report. Be thorough, be critical, be precise. Your work is vital for Roo Code's integrity."

---
## 3. Configuration Parameters for Validator Mode

The Validator Mode's behavior can be fine-tuned through several configuration parameters. These parameters can be set globally for the mode, or potentially overridden on a per-task basis by the Orchestrator or user when invoking validation.

1.  **`default_severity_threshold_for_escalation`**:
    *   **Description**: Defines the minimum severity level (e.g., "Medium", "High", "Critical") an identified issue must have to automatically trigger an escalation or specific workflow action (e.g., mandatory review by Orchestrator, halting a process).
    *   **Type**: Enum String
    *   **Possible Values**: "Low", "Medium", "High", "Critical"
    *   **Default**: "Medium"
    *   **Explanation**: Allows adjustment of how sensitive the system is to validation findings. A lower threshold means more issues get escalated.

2.  **`enable_positive_remarks`**:
    *   **Description**: Controls whether the Validator includes a "Positive Remarks" section in its report.
    *   **Type**: Boolean
    *   **Default**: `true`
    *   **Explanation**: While the primary focus is error detection, positive feedback can be valuable for mode calibration and user experience. Disabling this can make reports more concise if only errors are desired.

3.  **`default_confidence_threshold_for_reporting`**:
    *   **Description**: The minimum confidence level the Validator must have in a finding for it to be included in the report.
    *   **Type**: Enum String
    *   **Possible Values**: "Low", "Medium", "High"
    *   **Default**: "Low"
    *   **Explanation**: Helps filter out highly speculative findings if desired, making reports more focused on issues the Validator is more certain about.

4.  **`max_report_items_per_category`**:
    *   **Description**: Sets a maximum number of issues to be detailed per main error category (Content/Semantic, Code/Technical, etc.) in a single report. If more issues are found, a summary indicating additional findings in that category will be provided.
    *   **Type**: Integer
    *   **Default**: 20
    *   **Explanation**: Prevents overwhelmingly long reports for very problematic inputs. A value of `0` or `-1` could indicate no limit.

5.  **`error_category_weights`**:
    *   **Description**: A JSON object allowing differential weighting or prioritization for specific error categories or subcategories. This might influence the Validator's focus or the perceived overall risk of an input.
    *   **Type**: JSON Object
    *   **Example**:
        ```json
        {
          "Content/Semantic": {
            "Factual Inaccuracy": 1.5, // Higher weight
            "Logical Fallacies": 1.2
          },
          "Constraint/Guideline": {
            "Ethical Guideline Violations": 2.0 // Highest weight
          },
          "Code/Technical": 1.0 // Default weight for the category
        }
        ```
    *   **Default**: All categories/subcategories have a weight of `1.0`.
    *   **Explanation**: Allows tailoring the validation focus based on current project priorities or known risk areas. The Validator might use these weights to modulate its search depth or how it calculates an overall risk score (if implemented).

6.  **`custom_checklists_path`**:
    *   **Description**: Path to a directory containing custom checklist files (e.g., in Markdown or JSON format). These checklists can provide domain-specific or task-type-specific validation points.
    *   **Type**: String (File Path)
    *   **Default**: `null` (or a predefined path within `.roo/validator_checklists/`)
    *   **Explanation**: Enables extensibility. For example, a checklist for "Validating Research Summaries" or "Reviewing Python Code for Data Science Tasks." The Validator would be instructed to consult relevant checklists based on the `validation_focus_areas` or type of `content_to_validate`.

7.  **`suggestion_generation_level`**:
    *   **Description**: Controls the depth and effort put into generating `suggestion_for_correction`.
    *   **Type**: Enum String
    *   **Possible Values**: "None", "Brief", "Detailed"
    *   **Default**: "Brief"
    *   **Explanation**: "None" means no suggestions. "Brief" provides high-level guidance. "Detailed" might involve more elaborate (but still not full solution) suggestions, potentially increasing token usage.

8.  **`reference_document_access_policy`**:
    *   **Description**: Defines rules for how the Validator can access and use reference documents provided in `contextual_information` (e.g., `mode_configuration_architecture_analysis.md`).
    *   **Type**: JSON Object
    *   **Example**:
        ```json
        {
          "allow_full_read": true,
          "max_excerpt_length_for_cross_reference": 500, // characters
          "disallowed_keywords_in_references": ["internal_secret_key"]
        }
        ```
    *   **Default**: Full read allowed, no specific restrictions beyond general LLM safety.
    *   **Explanation**: Provides control over how contextual documents are used, especially if they contain sensitive or very large amounts of information.

These parameters provide a flexible framework for configuring the Validator mode to suit different needs and stages of a project.

---
## 4. Workflow Integration and Interaction with Other Error Mechanisms

The Validator mode is designed to be flexibly integrated into various Roo Code workflows, acting as a critical quality assurance layer. Its interaction with other modes and existing error mechanisms is key to its effectiveness.

### 4.1. Proposed Workflow Integration Points

1.  **User-Initiated Validation:**
    *   **How it works:** The user can explicitly invoke the Validator mode on any piece of content, whether it's an output from another Roo mode, a draft they've written, or external information.
    *   **Trigger:** Direct user command (e.g., "Roo, validate this code snippet for conceptual errors" or "Validate the previous research summary for factual accuracy").
    *   **Flow:** User → Validator → User (with report).

2.  **Orchestrator-Managed Validation (Post-Task):**
    *   **How it works:** The Orchestrator, after receiving a completed subtask from a specialist mode (e.g., Code, Research, Architect), can route the output to the Validator before presenting it to the user or using it in subsequent steps. This can be a default step for certain task types or triggered by risk assessment.
    *   **Trigger:** Orchestrator decision based on workflow rules, task type, risk score, or confidence level from the originating mode.
    *   **Flow:** Specialist Mode → Orchestrator → Validator → Orchestrator (with report) → [User | Next Mode | Revision by Original Mode].
    *   **Boomerang Logic:** The Validator receives the task from the Orchestrator and boomerangs the validation report back to the Orchestrator.

3.  **Conditional Automatic Validation (Inline Risk Mitigation):**
    *   **How it works:** If other, more lightweight error detection mechanisms (like semantic guardrails or consistency checkers) flag a potential issue with high uncertainty or severity, they can trigger an automatic handoff to the Validator for a more in-depth review.
    *   **Trigger:** High-risk flag from Semantic Guardrail (Task 1.1), Consistency Checker (Task 1.2), or Auto-Approval Safety Mechanism (Task 3.1) indicating a need for deeper scrutiny.
    *   **Flow:** Specialist Mode → [Guardrail/Checker Flag] → Validator → Orchestrator (with report).

4.  **Iterative Refinement Loop:**
    *   **How it works:** After validation identifies issues, the Orchestrator can send the output and the validation report back to the original specialist mode for revision. The revised output can then optionally undergo another round of validation.
    *   **Trigger:** Orchestrator decision based on Validator report findings.
    *   **Flow:** ... → Validator → Orchestrator → Original Mode (for revision with feedback) → Orchestrator → (Optional Re-Validation by Validator) → ...

5.  **Sandbox Output Validation:**
    *   **How it works:** Outputs generated within a Sandbox Mode (Task 2.3) could be routed through the Validator to assess their quality and safety before being considered for promotion or integration into the main project.
    *   **Trigger:** Completion of a task within Sandbox Mode.
    *   **Flow:** Sandbox Mode Task → Validator → Orchestrator/User (for review of sandboxed output and validation report).

### 4.2. Interaction with Other Error Mechanisms

The Validator mode complements, rather than replaces, other error detection and containment systems:

*   **Semantic Guardrails (Task 1.1) & Cross-Task Semantic Consistency Checkers (Task 1.2):**
    *   **Interaction:** These systems provide early warnings and can flag potential semantic drift or inconsistencies. The Validator can use these flags as input (`contextual_information`) to focus its analysis on specific areas of concern. If a guardrail flags a "medium risk" semantic deviation, the Validator can perform a deeper dive to confirm if it's a genuine error or a nuanced but acceptable output.
    *   **Benefit:** Validator provides a more thorough, context-aware assessment of issues preliminarily flagged by guardrails.

*   **Model-Specific Adaptation Layer (Task 1.3):**
    *   **Interaction:** The adaptation layer aims to normalize LLM behaviors. The Validator can help assess the effectiveness of this layer by checking if outputs still exhibit undesirable model-specific quirks or biases that the adaptation layer was meant to mitigate.
    *   **Benefit:** Validator's findings can provide feedback for tuning the adaptation layer.

*   **Task Boundary Enforcement (Task 2.1):**
    *   **Interaction:** If task boundary enforcement flags scope creep or irrelevant output, the Validator can confirm this assessment and analyze *why* the boundary was crossed (e.g., misinterpretation of prompt, over-eagerness of the generating mode).
    *   **Benefit:** Validator adds qualitative insight to quantitative boundary checks.

*   **Cost Anomaly Detection (Task 2.2):**
    *   **Interaction:** While not directly validating content, if a cost anomaly is detected for a task whose output is also being validated, it might (in conjunction with other factors) suggest the generating mode struggled, potentially leading to lower quality output requiring more stringent validation.
    *   **Benefit:** Indirect correlation; cost issues might hint at generation difficulties.

*   **Auto-Approval Safety Mechanisms (Task 3.1):**
    *   **Interaction:** If an output is auto-approved but with a "low confidence" or "requires review" flag, the Validator is an ideal candidate to perform that review. Conversely, if the Validator consistently finds no issues in outputs from a mode that frequently gets flagged by auto-approval, it might suggest the auto-approval thresholds are too conservative.
    *   **Benefit:** Validator acts as the human-like review step for conditional auto-approvals.

*   **Error Pattern Feedback Loop (Task 3.2):**
    *   **Interaction:** The structured error reports from the Validator are a prime source of input for the error pattern feedback loop. Identified and categorized errors, especially recurring ones, can be used to update rule sets for other modes, refine prompts, or even improve the Validator's own checklists.
    *   **Benefit:** Validator is a key data provider for systemic learning and improvement.

By integrating in these ways, the Validator mode becomes a central hub for advanced quality assurance, leveraging signals from other mechanisms and providing detailed feedback to improve the entire Roo Code system.

---
## 5. Examples of Validator Mode Processing

This section provides illustrative examples of how the Validator mode might process inputs and identify errors. These are conceptual and the actual LLM output would follow the structured JSON format defined in the system prompt.

### Example 1: Validating a Research Summary for Factual Accuracy and Citation

*   **`original_task_prompt`**: "Summarize the key findings of the attached paper 'Quantum_Entanglement_New_Horizons.pdf' focusing on its implications for teleportation. Limit summary to 200 words and include at least two direct quotes under 25 words each."
*   **`content_to_validate` (Excerpt from Research Mode output):**
    > "...The paper by Dr. Anya Sharma (2024) conclusively proves that macroscopic object teleportation is now feasible within 5 years. Sharma states, 'Our experiments demonstrate a 99.9% success rate in teleporting objects up to 1kg across distances of 10km, a breakthrough that will redefine logistics.' This suggests commercial teleportation systems are imminent. Another key finding is 'the energy requirements are surprisingly low, making it economically viable.'"
*   **`contextual_information`**:
    *   `Quantum_Entanglement_New_Horizons.pdf` (actual paper)
    *   Roo Code Citation Rules (max 1 quote, under 25 words, original summary)
*   **Validator Mode Output (Conceptual - actual would be JSON):**
    1.  **Overall Assessment:** Significant issues found, including factual misrepresentation and multiple citation violations.
    2.  **Validation Report Items:**
        *   **Issue VAL-001 (Critical, Content/Semantic, Factual Inaccuracy):**
            *   **Description:** Summary claims paper "conclusively proves macroscopic object teleportation feasible in 5 years."
            *   **Location:** Sentence 1 of `content_to_validate`.
            *   **Explanation:** The actual paper (Section 5.2) discusses theoretical models and small-scale particle entanglement, explicitly stating macroscopic teleportation remains a distant, highly speculative possibility, not proven or feasible in 5 years. This is a significant misrepresentation of the source material.
            *   **Suggestion:** Re-read paper's conclusion and discussion sections carefully. Adjust summary to accurately reflect the paper's actual claims about current feasibility.
            *   **Confidence:** High.
        *   **Issue VAL-002 (High, Constraint/Guideline, Citation Error):**
            *   **Description:** Two direct quotes are used.
            *   **Location:** Both quotes in `content_to_validate`.
            *   **Explanation:** Roo Code Citation Rule S1.2 specifies a maximum of ONE direct quote per summary.
            *   **Suggestion:** Select the most impactful quote and paraphrase the other, or integrate its essence into the summary.
            *   **Confidence:** High.
        *   **Issue VAL-003 (High, Constraint/Guideline, Citation Error):**
            *   **Description:** First quote "Our experiments demonstrate a 99.9% success rate in teleporting objects up to 1kg across distances of 10km, a breakthrough that will redefine logistics." is 26 words long.
            *   **Location:** First quote.
            *   **Explanation:** Roo Code Citation Rule S1.3 limits quotes to UNDER 25 words.
            *   **Suggestion:** Shorten the quote or paraphrase it.
            *   **Confidence:** High.
        *   **Issue VAL-004 (Medium, Content/Semantic, Misinterpretation):**
            *   **Description:** Statement "This suggests commercial teleportation systems are imminent."
            *   **Location:** Sentence 3.
            *   **Explanation:** This is an inferential leap not directly supported by the (misrepresented) quote or the actual paper, which is far more cautious.
            *   **Suggestion:** Remove or heavily qualify this speculative statement.
            *   **Confidence:** Medium.

### Example 2: Validating Code for Conceptual Error and Architectural Adherence

*   **`original_task_prompt`**: "Generate a Python function `calculate_average_price(products)` where `products` is a list of dictionaries, each with 'price' and 'quantity'. The function should return the average price per item, considering quantities."
*   **`content_to_validate` (Code Mode output):**
    ```python
    def calculate_average_price(products):
        total_price = 0
        total_items = 0
        if not products:
            return 0
        for product in products:
            total_price += product['price']
            total_items += 1 # Error: Should be product['quantity']
        return total_price / total_items
    ```
*   **`contextual_information`**:
    *   Project Architectural Guideline A2.1: "Functions handling financial data must include robust error handling for missing keys or invalid data types."
    *   Roo Code Python Style Guide P3.4: "All functions must have docstrings explaining parameters, return value, and any exceptions raised."
*   **Validator Mode Output (Conceptual):**
    1.  **Overall Assessment:** Critical conceptual error found in calculation logic, and non-adherence to architectural and style guidelines.
    2.  **Validation Report Items:**
        *   **Issue VAL-001 (Critical, Code/Technical, Conceptual Error in Code Logic):**
            *   **Description:** Average price calculation incorrectly uses item count instead of item quantity.
            *   **Location:** Line 7: `total_items += 1`
            *   **Explanation:** The prompt requires averaging price *per item considering quantities*. The current logic calculates average price per *product entry*, not per individual unit. It should be `total_items += product['quantity']` and `total_price += product['price'] * product['quantity']`.
            *   **Suggestion:** Revise accumulation logic for `total_price` to `product['price'] * product['quantity']` and `total_items` to use `product['quantity']`.
            *   **Confidence:** High.
        *   **Issue VAL-002 (High, Constraint/Guideline, Architectural Adherence):**
            *   **Description:** Missing error handling for potential `KeyError` if 'price' or 'quantity' keys are absent, or `TypeError` for non-numeric values.
            *   **Location:** Function body, lines 6-8.
            *   **Explanation:** Architectural Guideline A2.1 mandates robust error handling for financial data functions. The code lacks `try-except` blocks or checks for key existence/type.
            *   **Suggestion:** Implement `try-except` blocks for `KeyError` and `TypeError` within the loop, or add pre-checks for key validity and data types. Define behavior for invalid product entries (e.g., skip, raise error, log).
            *   **Confidence:** High.
        *   **Issue VAL-003 (Medium, Constraint/Guideline, Style Guide Adherence):**
            *   **Description:** Function lacks a docstring.
            *   **Location:** Line 1, before function definition.
            *   **Explanation:** Roo Code Python Style Guide P3.4 requires all functions to have docstrings.
            *   **Suggestion:** Add a comprehensive docstring explaining parameters, return value, and potential exceptions.
            *   **Confidence:** High.

These examples illustrate the depth and breadth of validation the Validator mode aims to provide, going beyond surface-level checks to ensure outputs are accurate, robust, and aligned with project standards.